<template>
  <Transition
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-in duration-150"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="modelValue"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/75"
      aria-modal="true"
      role="dialog"
    >
      <Transition
        enter-active-class="transition ease-out duration-300"
        enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        enter-to-class="opacity-100 translate-y-0 sm:scale-100"
        leave-active-class="transition ease-in duration-200"
        leave-from-class="opacity-100 translate-y-0 sm:scale-100"
        leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
      >
        <div
          v-if="modelValue"
          class="relative w-full max-w-5xl mx-4 bg-white rounded-lg shadow-xl"
        >
          <!-- Header -->
          <div class="flex items-center justify-between px-6 py-2 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">
              Chi tiết Tài sản Cống Đập
            </h3>
            <!-- <button
              @click="closeModal"
              class="text-gray-400 hover:text-gray-600 focus:outline-none"
              aria-label="Đóng modal"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button> -->
          </div>

          <!-- Body -->
          <div class="px-6 py-2 overflow-y-auto" style="max-height: 75vh;">
            <!-- Trạng thái Loading -->
            <div v-if="isLoading" class="flex items-center justify-center py-10">
              <svg class="w-8 h-8 mr-3 -ml-1 text-blue-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="text-gray-600">Đang tải dữ liệu...</span>
            </div>

            <!-- Trạng thái Lỗi -->
            <div v-else-if="error" class="px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
              <strong class="font-bold">Lỗi!</strong>
              <span class="block sm:inline"> {{ error }}</span>
            </div>

            <!-- Hiển thị dữ liệu -->
            <div v-else-if="displayFields.length > 0" class="flex flex-col lg:flex-row gap-4">
              <!-- Cột thông tin chi tiết (trái) -->
              <div class="w-full lg:w-3/5 space-y-2">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  <template v-for="field in displayFields" :key="field.key">
                    <div v-if="assetData && assetData.data && assetData.data.properties" class="space-y-1">
                      <label :for="field.key" class="block text-sm font-medium text-gray-700">{{ field.label }}</label>
                      <VueDatePicker
                        v-if="field.key === 'nam_xd' || field.key === 'nam_sd'"
                        v-model.number="assetData.data.properties[field.key]"
                        year-picker
                        :name="field.key"
                        :id="field.key"
                        placeholder="Chọn năm"
                        :year-range="[1900, new Date().getFullYear() + 10]"
                        format="yyyy"
                        auto-apply
                        :clearable="true"
                        class="w-full"
                      />
                      <select
                        v-else-if="field.key === 'phuongthuc'"
                        :name="field.key"
                        :id="field.key"
                        v-model="assetData.data.properties[field.key]"
                        class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="" disabled>Chọn phương thức</option>
                        <option v-for="option in phuongThucOptions" :key="option.value" :value="option.value">
                          {{ option.label }}
                        </option>
                        <option v-if="assetData?.data?.properties?.[field.key] && !phuongThucOptions.find(opt => opt.value === assetData?.data?.properties?.[field.key])" :value="assetData?.data?.properties?.[field.key]">
                          {{ assetData.data.properties[field.key] }} (Hiện tại)
                        </option>
                      </select>
                      <select
                        v-else-if="field.key === 'loai_ct'"
                        :name="field.key"
                        :id="field.key"
                        v-model="assetData.data.properties[field.key]"
                        class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      >
                        <option value="" disabled>Chọn loại công trình</option>
                        <option value="Nhỏ">Nhỏ</option>
                        <option value="Vừa">Vừa</option>
                        <option value="Lớn">Lớn</option>
                        <!-- Tùy chọn hiển thị giá trị hiện tại nếu nó không nằm trong danh sách cố định -->
                        <option v-if="assetData?.data?.properties?.[field.key] && !['Nhỏ', 'Vừa', 'Lớn'].includes(assetData?.data?.properties?.[field.key])" :value="assetData?.data?.properties?.[field.key]">
                          {{ assetData.data.properties[field.key] }} (Hiện tại)
                        </option>
                      </select>
                      <template v-else-if="field.key === 'id_qt'">
                        <select
                          :name="field.key"
                          :id="field.key"
                          v-model="assetData.data.properties[field.key]"
                          :disabled="isLoadingQuyetToan"
                          class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                          <option value="" disabled>--- Chọn quyết toán ---</option>
                          <option v-for="option in quyetToanOptions" :key="option.id" :value="option.id">
                            {{ option.qd_quyettoan }}
                          </option>
                        </select>
                        <div v-if="quyetToanError" class="mt-1 text-sm text-red-600">
                          {{ quyetToanError }}
                        </div>
                      </template>
                      <input
                        v-else-if="field.key === 'nguyengia'"
                        type="number"
                        :name="field.key"
                        :id="field.key"
                        v-model.number="assetData.data.properties[field.key]"
                        class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-100 cursor-not-allowed"
                        disabled
                        placeholder="Giá trị sẽ tự động cập nhật"
                      />
                      <input
                        v-else-if="field.key === 'dt_dat'"
                        type="number"
                        :name="field.key"
                        :id="field.key"
                        :step="field.key === 'dt_dat' ? 'any' : '1'"
                        v-model.number="assetData.data.properties[field.key]"
                        class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      />
                       <textarea
                        v-else-if="field.key === 'chuthich'"
                        :name="field.key"
                        :id="field.key"
                        v-model="assetData.data.properties[field.key]"
                        rows="2"
                        class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      ></textarea>
                      <input
                        v-else
                        type="text"
                        :name="field.key"
                        :id="field.key"
                        v-model="assetData.data.properties[field.key]"
                        :readonly="field.key === 'id_xa' || field.key === 'tenxa'"
                        :class="[
                          'block w-full p-2 border border-gray-300 rounded-md shadow-sm sm:text-sm',
                          field.key === 'id_xa' || field.key === 'tenxa'
                            ? 'bg-gray-100 text-gray-600 cursor-not-allowed'
                            : 'focus:ring-blue-500 focus:border-blue-500'
                        ]"
                      />
                    </div>
                  </template>
                </div>
              </div>
              <!-- Cột bản đồ (phải) -->
              <div class="w-full lg:w-2/5">
                <div ref="mapContainer" class="h-80 lg:h-full rounded-md bg-gray-200">
                  <!-- Leaflet map will be initialized here -->
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="flex items-center justify-end px-6 py-2 space-x-3 bg-gray-50 rounded-b-lg">
            <button
              @click="closeModal"
              type="button"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Đóng
            </button>
            <button
              @click="handleUpdateAsset"
              type="button"
              :disabled="isUpdating"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isUpdating" class="flex items-center"><svg class="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Đang lưu...</span>
              <span v-else>Cập nhật</span>
            </button>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed, nextTick } from 'vue';
import type { PropType } from 'vue';
import axios from 'axios';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-draw/dist/leaflet.draw.css'; // Import CSS for leaflet-draw

// Import VueDatePicker
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';



import 'leaflet-draw'; // Import JS for leaflet-draw (adds L.Control.Draw, L.Draw.Event, etc.)

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  assetId: {
    type: [Number, String, null] as PropType<number | string | null>,
    required: true,
  },
});

// Import useDataRefresh composable
import { useDataRefresh, RefreshEventType } from '@/composables/useDataRefresh';

// Emits
const emit = defineEmits(['update:modelValue', 'asset-updated']);

// Sử dụng data refresh composable
const { triggerRefresh, triggerCacheInvalidation } = useDataRefresh();

// State
const assetData = ref<Record<string, any> | null>(null);
const isUpdating = ref(false); // For update operation
const isLoading = ref(false);
const error = ref<string | null>(null);
const mapContainer = ref<HTMLElement | null>(null);
const mapInstance = ref<L.Map | null>(null);
const editableLayers = ref<L.FeatureGroup | null>(null); // For leaflet-draw

interface YearMonth {
  year: number;
  month: number;
}

// Định nghĩa các tùy chọn cho trường "Phương thức quản lý"
const phuongThucOptions = ref([
  { value: 'Sử dụng chung', label: 'Sử dụng chung' },
  { value: 'Trực tiếp tổ chức thực hiện khai thác', label: 'Trực tiếp tổ chức thực hiện khai thác' },
]);

const drawControl = ref<L.Control.Draw | null>(null);

// Reset state functions
const resetModalState = () => {
  isUpdating.value = false;
  error.value = null;
  assetData.value = null;
  isLoading.value = false;
  // Reset map instance
  destroyMap();
};

// Close Modal Logic
const closeModal = () => {
  isUpdating.value = false;
  error.value = null;
  assetData.value = null;
  // Clean up map instance
  destroyMap();
  // Emit close event
  emit('update:modelValue', false);
};

// Fetch Data Logic
const fetchAssetDetails = async () => {
  if (!props.assetId) {
    error.value = "Không có ID tài sản được cung cấp.";
    return;
  }

  // Reset state for each new fetch
  isLoading.value = true;
  error.value = null;
  assetData.value = null;

  try {
    const response = await axios.get(`/api/taisan/congdap/${props.assetId}`);
    assetData.value = response.data;
  } catch (err: any) {
    console.error('Failed to fetch asset details:', err);
    error.value = 'Không thể tải dữ liệu tài sản. Vui lòng thử lại.';
  } finally {
    isLoading.value = false;
  }
};

// Constants for API timeout and error messages
const API_TIMEOUT = 30000; // 30 seconds
const ERROR_MESSAGES = {
  TIMEOUT: 'Thao tác đã hết thời gian chờ. Vui lòng thử lại.',
  NETWORK: 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.',
  UNKNOWN: 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
};

// Update Asset Logic with proper async handling
const handleUpdateAsset = async () => {
  if (!props.assetId || !assetData.value?.data) {
    error.value = "Không có dữ liệu để cập nhật.";
    return;
  }

  isUpdating.value = true;
  error.value = null;

  try {
    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('TIMEOUT')), API_TIMEOUT);
    });

    // Create the API request promise
    const apiPromise = axios.put(
      `/api/taisan/congdap/${props.assetId}`, 
      assetData.value.data,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    // Race between timeout and API request
    const response = await Promise.race([apiPromise, timeoutPromise]);

    // Validate response success
    if (!response?.data?.success && !response?.status?.toString().startsWith('2')) {
      throw new Error(response?.data?.message || 'Lỗi không xác định từ server');
    }

    // If we reach here, update was successful
    try {
      // Emit the update event before closing
      emit('asset-updated', props.assetId);

      // Trigger global refresh với specific event type
      triggerRefresh(RefreshEventType.CONGDAP_UPDATED, {
        assetId: props.assetId
      });

      // Trigger cache invalidation để đảm bảo Map.vue sẽ fetch fresh data
      triggerCacheInvalidation(['congdap-list']);

      // Add a small delay for visual feedback
      await new Promise(resolve => setTimeout(resolve, 200));

      // Close the modal and reset state
      closeModal();
    } catch (emitError) {
      console.error('Error during post-update handling:', emitError);
      // Still close the modal since the update was successful
      closeModal();
    }

  } catch (err: any) {
    console.error('Error updating asset:', err);
    
    // Handle different types of errors
    if (err.message === 'TIMEOUT') {
      error.value = ERROR_MESSAGES.TIMEOUT;
    } else if (err.isAxiosError && !err.response) {
      // Network error
      error.value = ERROR_MESSAGES.NETWORK;
    } else {
      // Server error or other errors
      error.value = err.response?.data?.message 
        || err.message 
        || ERROR_MESSAGES.UNKNOWN;
    }
    
    // Keep the modal open for error state
    isUpdating.value = false;
  }
};

// Function to find xa by coordinates using spatial query
const findXaByCoordinates = async (longitude: number, latitude: number) => {
  try {
    const response = await axios.post('/api/spatial/find-xa-by-coordinates', {
      longitude,
      latitude
    });

    if (response.data.success && response.data.data) {
      // Update id_xa and ten_xa in assetData
      if (assetData.value?.data?.properties) {
        assetData.value.data.properties.id_xa = response.data.data.id_xa;
        assetData.value.data.properties.tenxa = response.data.data.ten_xa; // Changed ten_xa to tenxa
        console.log('Thông tin xã đã được cập nhật:', {
          id_xa: response.data.data.id_xa,
          tenxa: response.data.data.ten_xa // Changed ten_xa to tenxa
        });
      }
    } else {
      // Clear xa information if not found
      if (assetData.value?.data?.properties) {
        assetData.value.data.properties.id_xa = null;
        assetData.value.data.properties.tenxa = null; // Changed ten_xa to tenxa
        console.log('Không tìm thấy xã tại tọa độ này');
      }
    }
  } catch (error: any) {
    console.error('Lỗi khi tìm kiếm thông tin xã:', error);
    // Don't clear existing xa information on error to avoid data loss
  }
};

// Helper to update asset coordinates
const updateAssetCoordinates = async (latlng: L.LatLng) => {
  if (assetData.value?.data) {
    if (!assetData.value.data.geometry) {
      assetData.value.data.geometry = { type: 'Point', coordinates: [0,0] }; // Initialize if not present
    }
    assetData.value.data.geometry.type = 'Point';
    assetData.value.data.geometry.coordinates = [latlng.lng, latlng.lat];

    // Automatically find xa information when coordinates change
    await findXaByCoordinates(latlng.lng, latlng.lat);

    // console.log('Asset coordinates updated:', assetData.value.data.geometry.coordinates);
    // If you need to inform the parent component about the change, emit an event here
    // emit('coordinates-updated', assetData.value.data.geometry.coordinates);
  }
};

const initMap = async () => {
  if (mapContainer.value && !mapInstance.value && assetData.value?.data) {
    const lat = assetData.value?.data?.geometry?.coordinates[1] || 10.7769; // Default to HCMC lat
    const lng = assetData.value?.data?.geometry?.coordinates[0] || 106.7009; // Default to HCMC lng
    const zoom = assetData.value?.data?.geometry ? 17 : 12; // Zoom closer if specific coords exist

    mapInstance.value = L.map(mapContainer.value).setView([lat, lng], zoom);
    L.tileLayer(
      'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
      { attribution: '&copy; <a href="https://carto.com/">CartoDB</a>' }
    ).addTo(mapInstance.value);

    // Initialize FeatureGroup for draw controls
    editableLayers.value = new L.FeatureGroup();
    mapInstance.value.addLayer(editableLayers.value);

    // Add existing marker to editableLayers if geometry exists
    if (editableLayers.value && assetData.value?.data?.geometry?.type === 'Point' && assetData.value.data.geometry.coordinates) {
      const marker = L.marker([lat, lng], { draggable: true })
        .bindPopup(assetData.value.data.properties?.ten || 'Vị trí tài sản');

      // Add drag event listener to existing marker
      marker.on('dragend', async (event) => {
        const draggedMarker = event.target as L.Marker;
        await updateAssetCoordinates(draggedMarker.getLatLng());
      });

      editableLayers.value.addLayer(marker);
      // marker.openPopup(); // Optionally open popup
    }

    // Initialize Draw Control
    const drawOptions = {
      position: 'topright',
      draw: {
        polygon: false,
        polyline: false,
        rectangle: false,
        circle: false,
        circlemarker: false,
        marker: { draggable: true }, // Enable draggable for new markers
      },
      edit: {
        featureGroup: editableLayers.value! as any, // Ép kiểu để giải quyết vấn đề tương thích
        remove: true,
      },
    };
    // Ép kiểu drawOptions thành any khi khởi tạo L.Control.Draw
    // để tránh lỗi không tương thích giữa DrawOptions và DrawConstructorOptions
    drawControl.value = new L.Control.Draw(drawOptions as any); 
    // Ép kiểu drawControl.value thành any khi thêm vào bản đồ
    mapInstance.value.addControl(drawControl.value as any); 

    // Event for when a new shape is created
    mapInstance.value.on(L.Draw.Event.CREATED, async (event) => {
      const layer = (event as L.DrawEvents.Created).layer;
      if (layer instanceof L.Marker) {
        // Add drag event listener to new marker
        layer.on('dragend', async (dragEvent) => {
          const draggedMarker = dragEvent.target as L.Marker;
          await updateAssetCoordinates(draggedMarker.getLatLng());
        });

        editableLayers.value?.clearLayers(); // Clear previous markers, assuming only one point
        editableLayers.value?.addLayer(layer);
        await updateAssetCoordinates(layer.getLatLng());
      }
    });

    // Event for when shapes are edited
    mapInstance.value.on(L.Draw.Event.EDITED, (event) => {
      const layers = (event as L.DrawEvents.Edited).layers;
      layers.eachLayer((layer) => {
        if (layer instanceof L.Marker) {
          updateAssetCoordinates(layer.getLatLng());
        }
      });
    });

     // Event for when shapes are deleted
     mapInstance.value.on(L.Draw.Event.DELETED, () => {
        if (assetData.value?.data?.geometry) {
            // assetData.value.data.geometry = null; // Or handle as per your logic
            // console.log('Asset geometry removed');
        }
    });
  }
};
const destroyMap = () => {
  if (mapInstance.value) {
    mapInstance.value.remove();
    mapInstance.value = null;
  }
};

// Watch for modal open
watch(() => props.modelValue, async (newValue) => {
  if (newValue) {
    document.body.style.overflow = 'hidden';
    await fetchAssetDetails(); // Wait for data to be fetched
    if (!error.value) { // Proceed even if assetData is null initially (for new asset)
      await nextTick(); // Ensure the DOM is updated and mapContainer is available
      await initMap(); // initMap now handles assetData.value internally
    }
    // } // Removed this condition to allow map init even for new assets
  } else {
    document.body.style.overflow = '';
    resetModalState(); // Use the new resetModalState function
  }
});

// Handle Escape key for closing modal
const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && props.modelValue) {
    closeModal();
  }
};

onMounted(() => {
  window.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown);
  destroyMap(); // Ensure map is destroyed
  // Ensure body scroll is restored if component is unmounted while modal is open
  document.body.style.overflow = '';
});

// Mapping for Vietnamese field names
const vietnameseFieldNames: Record<string, string> = {
  id_qt: "QĐ Quyết Toán",
  nguyengia: "Nguyên Giá",
  ten: "Tên Công Trình",
  quymo_ct: "Quy Mô Công Trình",
  loai_ct: "Loại Công Trình",
  nam_xd: "Năm Xây Dựng",
  nam_sd: "Năm Sử Dụng",
  dt_dat: "Diện Tích Đất (m²)",
  tinhtrang: "Tình Trạng",
  quytrinh_vh: "Quy Trình Vận Hành",
  quytrinh_bt: "Quy Trình Bảo Trì",
  dv_quanly: "Đơn Vị Quản Lý",
  phuongthuc: "Phương Thức Quản Lý",
  chuthich: "Chú Thích",
  //id_xa: "ID Xã",
  tenxa: "Tên Xã" // Changed ten_xa to tenxa
};

// Define the desired order of fields for display
// Bạn có thể thay đổi thứ tự các 'key' trong mảng này để điều chỉnh thứ tự hiển thị
const fieldOrder: string[] = [
  "ten",           // Tên Công Trình
  "loai_ct",       // Loại Công Trình
  "quymo_ct",      // Quy Mô Công Trình
  "tinhtrang",     // Tình Trạng
  "nam_xd",        // Năm Xây Dựng
  "nam_sd",        // Năm Sử Dụng
  //"id_xa",         // ID Xã
  "tenxa",        // Tên Xã // Changed ten_xa to tenxa
  "id_qt",         // ID Quyết Toán
  "nguyengia",     // Nguyên Giá
  "dt_dat",        // Diện Tích Đất (m²)
  "dv_quanly",     // Đơn Vị Quản Lý
  "phuongthuc",    // Phương Thức Quản Lý
  "quytrinh_vh",   // Quy Trình Vận Hành
  "quytrinh_bt",   // Quy Trình Bảo Trì
  "chuthich"       // Chú Thích
];

const displayFields = computed(() => {
  if (!assetData.value?.data?.properties) {
    return [];
  }
  const properties = assetData.value.data.properties;
  const orderedFields: { key: string; value: any; label: string }[] = [];
  const availableKeys = Object.keys(properties);

  // Add fields based on fieldOrder
  fieldOrder.forEach(key => {
    if (availableKeys.includes(key)) {
      orderedFields.push({ key, value: properties[key], label: formatLabel(key) });
    }
  });

  // Add any remaining fields not in fieldOrder (e.g., new fields from API)
  /* availableKeys.forEach(key => {
    if (!fieldOrder.includes(key)) {
      orderedFields.push({ key, value: properties[key], label: formatLabel(key) });
    }
  }); */
  return orderedFields;
});

// Helper function to format snake_case keys to Title Case labels
const formatLabel = (key: string): string => {
  if (!key) return '';
  if (vietnameseFieldNames[key]) {
    return vietnameseFieldNames[key];
  }
  const words = key.split('_');
  return words
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// New code starts here
interface QuyetToanOption {
  id: string;
  nguyengia: number | null; // Thêm trường nguyengia
  [key: string]: any; // Cho phép các thuộc tính khác nếu API trả về
}
const quyetToanOptions = ref<QuyetToanOption[]>([]);
const isLoadingQuyetToan = ref(false);
const quyetToanError = ref<string | null>(null);

const loadQuyetToanOptions = async () => {
  try {
    isLoadingQuyetToan.value = true;
    quyetToanError.value = null;
    const response = await fetch('/api/quyet-toan-options');
    if (!response.ok) {
      throw new Error('Không thể tải danh sách quyết toán');
    }
    const data = await response.json();
    quyetToanOptions.value = data.data || [];
  } catch (error: any) {
    console.error('Error loading quyet toan options:', error);
    quyetToanError.value = error.message || 'Có lỗi xảy ra khi tải danh sách quyết toán.';
  } finally {
    isLoadingQuyetToan.value = false;
  }
};

onMounted(() => {
  loadQuyetToanOptions();
});

// Watch for changes in id_qt to update nguyengia
watch(() => assetData.value?.data?.properties?.id_qt, (newIdQt) => {
  if (assetData.value?.data?.properties) {
    if (newIdQt && quyetToanOptions.value.length > 0) {
      const selectedQuyetToan = quyetToanOptions.value.find(option => option.id === newIdQt);
      if (selectedQuyetToan && typeof selectedQuyetToan.nguyengia !== 'undefined') {
        assetData.value.data.properties.nguyengia = selectedQuyetToan.nguyengia;
      } else {
        // Nếu không tìm thấy hoặc không có nguyengia, có thể đặt là null hoặc 0
        assetData.value.data.properties.nguyengia = null;
      }
    } else {
      // Nếu id_qt bị xóa, cũng xóa nguyengia
      assetData.value.data.properties.nguyengia = null;
    }
  }
});
// New code ends here
</script>
