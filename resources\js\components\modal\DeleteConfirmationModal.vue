<template>
  <Transition
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-in duration-150"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="modelValue"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/75"
      aria-modal="true"
      role="dialog"
    >
      <Transition
        enter-active-class="transition ease-out duration-300"
        enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        enter-to-class="opacity-100 translate-y-0 sm:scale-100"
        leave-active-class="transition ease-in duration-200"
        leave-from-class="opacity-100 translate-y-0 sm:scale-100"
        leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
      >
        <div
          v-if="modelValue"
          class="relative w-full max-w-xl mx-4 bg-white rounded-lg shadow-xl"
        >
          <!-- Header -->
          <div class="flex items-center justify-between px-6 py-2 border-b border-gray-200">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-800">
                  Xác nhận xóa tài sản
                </h3>
              </div>
            </div>
          </div>

          <!-- Body -->
          <div class="px-6 py-4">
            <!-- Trạng thái Loading -->
            <div v-if="isLoading" class="flex items-center justify-center py-6">
              <svg class="w-6 h-6 mr-3 -ml-1 text-blue-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="text-gray-600">Đang tải thông tin...</span>
            </div>

            <!-- Trạng thái Lỗi -->
            <div v-else-if="error" class="px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
              <strong class="font-bold">Lỗi!</strong>
              <span class="block sm:inline"> {{ error }}</span>
            </div>

            <!-- Nội dung xác nhận -->
            <div v-else class="space-y-4">
              <!-- Warning message -->
              <div class="p-4 bg-red-50 border border-red-200 rounded-md">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                      Cảnh báo
                    </h3>
                    <div class="text-sm text-red-700">
                      <p>Hành động này không thể hoàn tác. Tài sản sẽ bị xóa vĩnh viễn khỏi hệ thống.</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Asset information -->
              <div v-if="assetData" class="space-y-3">
                <h4 class="text-sm font-medium text-gray-900">Thông tin tài sản sẽ bị xóa:</h4>
                <div class="bg-gray-50 p-3 rounded-md space-y-2">
                  <div v-if="assetData.properties?.ten" class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Tên công trình:</span>
                    <span class="text-sm text-gray-900">{{ assetData.properties.ten }}</span>
                  </div>
                  <div v-if="assetData.properties?.loai_ct" class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Loại công trình:</span>
                    <span class="text-sm text-gray-900">{{ assetData.properties.loai_ct }}</span>
                  </div>
                  <div v-if="assetData.properties?.dv_quanly" class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Đơn vị quản lý:</span>
                    <span class="text-sm text-gray-900">{{ assetData.properties.dv_quanly }}</span>
                  </div>
                 <!--  <div v-if="assetData.id" class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">ID:</span>
                    <span class="text-sm text-gray-900 font-mono">{{ assetData.id }}</span>
                  </div> -->
                </div>
              </div>

              <!-- Confirmation text -->
              <div class="text-sm text-gray-700">
                <p>Bạn có chắc chắn muốn xóa tài sản này không?</p>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="flex items-center justify-end px-6 py-2 space-x-3 bg-gray-50 rounded-b-lg">
            <button
              @click="closeModal"
              type="button"
              :disabled="isDeleting"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Hủy
            </button>
            <button
              @click="handleDelete"
              type="button"
              :disabled="isDeleting || isLoading || !!error"
              class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isDeleting" class="flex items-center">
                <svg class="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Đang xóa...
              </span>
              <span v-else>Xác nhận xóa</span>
            </button>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';
import type { PropType } from 'vue';
import axios from 'axios';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  assetId: {
    type: [Number, String, null] as PropType<number | string | null>,
    required: true,
  },
});

// Import useDataRefresh composable
import { useDataRefresh, RefreshEventType } from '@/composables/useDataRefresh';

// Emits
const emit = defineEmits(['update:modelValue', 'asset-deleted']);

// Sử dụng data refresh composable
const { triggerRefresh, triggerCacheInvalidation } = useDataRefresh();

// State
const assetData = ref<Record<string, any> | null>(null);
const isLoading = ref(false);
const isDeleting = ref(false);
const error = ref<string | null>(null);

// Constants for API timeout and error messages
const API_TIMEOUT = 30000; // 30 seconds
const ERROR_MESSAGES = {
  TIMEOUT: 'Thao tác đã hết thời gian chờ. Vui lòng thử lại.',
  NETWORK: 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.',
  UNKNOWN: 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
};

// Reset state functions
const resetModalState = () => {
  isLoading.value = false;
  isDeleting.value = false;
  error.value = null;
  assetData.value = null;
};

// Close Modal Logic
const closeModal = () => {
  resetModalState();
  emit('update:modelValue', false);
};

// Fetch Asset Data Logic
const fetchAssetDetails = async () => {
  if (!props.assetId) {
    error.value = "Không có ID tài sản được cung cấp.";
    return;
  }

  isLoading.value = true;
  error.value = null;
  assetData.value = null;

  try {
    const response = await axios.get(`/api/taisan/congdap/${props.assetId}`);
    assetData.value = response.data.data;
  } catch (err: any) {
    console.error('Failed to fetch asset details:', err);
    error.value = 'Không thể tải dữ liệu tài sản. Vui lòng thử lại.';
  } finally {
    isLoading.value = false;
  }
};

// Delete Asset Logic
const handleDelete = async () => {
  if (!props.assetId) {
    error.value = "Không có ID tài sản để xóa.";
    return;
  }

  isDeleting.value = true;
  error.value = null;

  try {
    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('TIMEOUT')), API_TIMEOUT);
    });

    // Create the API request promise
    const apiPromise = axios.delete(`/api/taisan/congdap/${props.assetId}`);

    // Race between timeout and API request
    const response = await Promise.race([apiPromise, timeoutPromise]);

    // Validate response success
    if (!response?.data?.success && !response?.status?.toString().startsWith('2')) {
      throw new Error(response?.data?.message || 'Lỗi không xác định từ server');
    }

    // If we reach here, deletion was successful
    try {
      // Emit the delete event before closing
      emit('asset-deleted', props.assetId);

      // Trigger global refresh với specific event type
      triggerRefresh(RefreshEventType.CONGDAP_DELETED, {
        assetId: props.assetId
      });

      // Trigger cache invalidation để đảm bảo Map.vue sẽ fetch fresh data
      triggerCacheInvalidation(['congdap-list']);

      // Add a small delay for visual feedback
      await new Promise(resolve => setTimeout(resolve, 200));

      // Close the modal and reset state
      closeModal();
    } catch (emitError) {
      console.error('Error during post-delete handling:', emitError);
      // Still close the modal since the deletion was successful
      closeModal();
    }

  } catch (err: any) {
    console.error('Error deleting asset:', err);
    
    // Handle different types of errors
    if (err.message === 'TIMEOUT') {
      error.value = ERROR_MESSAGES.TIMEOUT;
    } else if (err.isAxiosError && !err.response) {
      // Network error
      error.value = ERROR_MESSAGES.NETWORK;
    } else {
      // Server error or other errors
      error.value = err.response?.data?.message 
        || err.message 
        || ERROR_MESSAGES.UNKNOWN;
    }
    
    // Keep the modal open for error state
    isDeleting.value = false;
  }
};

// Watch for modal open
watch(() => props.modelValue, async (newValue) => {
  if (newValue) {
    document.body.style.overflow = 'hidden';
    await fetchAssetDetails();
  } else {
    document.body.style.overflow = '';
    resetModalState();
  }
});

// Handle Escape key for closing modal
const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && props.modelValue) {
    closeModal();
  }
};

onMounted(() => {
  window.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown);
  // Ensure body scroll is restored if component is unmounted while modal is open
  document.body.style.overflow = '';
});
</script>
