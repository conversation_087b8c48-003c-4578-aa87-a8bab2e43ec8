<template>
  <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
    <h2 class="text-xl font-semibold mb-4"><PERSON><PERSON></h2>
    <div class="flex gap-2 mb-4">
      <button class="px-3 py-1.5 text-sm font-medium rounded-full bg-blue-100 text-blue-800"><PERSON><PERSON><PERSON> (6)</button>
      <button class="px-3 py-1.5 text-sm font-medium rounded-full bg-gray-100 text-gray-800">Thêm mới (3)</button>
      <button class="px-3 py-1.5 text-sm font-medium rounded-full bg-gray-100 text-gray-800">Chỉnh sửa (2)</button>
      <button class="px-3 py-1.5 text-sm font-medium rounded-full bg-gray-100 text-gray-800">Xóa (1)</button>
    </div>
    <DataTable
      :columnDefs="approveColumns"
      :rowData="approveRowData"
      :gridOptions="gridOptions"
      theme="ag-theme-tskchtl"
      height="calc(100vh - 450px)"
    />
    <div class="mt-4 flex justify-between items-center">
      <p class="text-sm text-gray-600">Đã chọn 0 mục</p>
      <div class="flex gap-3">
        <button class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
          <Icon name="x" class="inline -ml-1 mr-2 h-5 w-5" />
          Từ chối đã chọn
        </button>
        <button class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
          <Icon name="check" class="inline -ml-1 mr-2 h-5 w-5" />
          Phê duyệt đã chọn
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue'
import DataTable from '@/components/DataTable.vue'
import Icon from '@/components/Icon.vue'
import type { ColDef } from 'ag-grid-community'

const gridOptions = {
  pagination: true,
  paginationPageSize: 10,
  suppressRowClickSelection: true,
  rowSelection: 'multiple',
}

const ActionTypeRenderer = (params: any) => {
  const action = params.value
  let colorClasses = ''
  if (action === 'Thêm mới') {
    colorClasses = 'bg-blue-100 text-blue-800'
  } else if (action === 'Chỉnh sửa') {
    colorClasses = 'bg-yellow-100 text-yellow-800'
  } else if (action === 'Xóa') {
    colorClasses = 'bg-red-100 text-red-800'
  }
  return h('span', { class: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${colorClasses}` }, action)
}

const ApproveActionRenderer = (params: any) => {
  const approveBtn = h('button', { class: 'p-1 text-green-600 hover:text-green-800', onClick: () => console.log('Approve', params.data) }, [h(Icon, { name: 'check', class: 'h-5 w-5' })])
  const rejectBtn = h('button', { class: 'p-1 text-red-600 hover:red-800', onClick: () => console.log('Reject', params.data) }, [h(Icon, { name: 'x', class: 'h-5 w-5' })])
  return h('div', { class: 'flex gap-2' }, [approveBtn, rejectBtn])
}

const approveColumns: ColDef[] = [
  { headerName: 'MÃ TÀI SẢN', field: 'ma_tai_san', checkboxSelection: true, headerCheckboxSelection: true, width: 150 },
  { headerName: 'TÊN TÀI SẢN', field: 'ten_tai_san', flex: 1, minWidth: 200 },
  { headerName: 'LOẠI', field: 'loai', width: 120 },
  { headerName: 'HÀNH ĐỘNG', field: 'hanh_dong', width: 120, cellRenderer: ActionTypeRenderer },
  { headerName: 'NGƯỜI CẬP NHẬT', field: 'nguoi_cap_nhat', width: 150 },
  { headerName: 'NGÀY CẬP NHẬT', field: 'ngay_cap_nhat', width: 180 },
  { headerName: 'THAO TÁC', field: 'thao_tac', width: 100, cellRenderer: ApproveActionRenderer, sortable: false, filter: false }
]

const approveRowData = ref([
  { id: 1, ma_tai_san: 'TS-00131', ten_tai_san: 'Đập Tuyên Quang', loai: 'Đập', hanh_dong: 'Thêm mới', nguoi_cap_nhat: 'Nguyễn Văn B', ngay_cap_nhat: '09/06/2025 10:25' },
  { id: 2, ma_tai_san: 'TS-00125', ten_tai_san: 'Trạm bơm Liên Mạc', loai: 'Trạm bơm', hanh_dong: 'Chỉnh sửa', nguoi_cap_nhat: 'Trần Thị C', ngay_cap_nhat: '08/06/2025 15:40' },
  { id: 3, ma_tai_san: 'TS-00129', ten_tai_san: 'Kênh Đồng Câu Long', loai: 'Kênh', hanh_dong: 'Chỉnh sửa', nguoi_cap_nhat: 'Lê Văn D', ngay_cap_nhat: '08/06/2025 11:15' },
  { id: 4, ma_tai_san: 'TS-00132', ten_tai_san: 'Cống Cái Lớn', loai: 'Cống', hanh_dong: 'Thêm mới', nguoi_cap_nhat: 'Phạm Thị E', ngay_cap_nhat: '07/06/2025 16:30' },
  { id: 5, ma_tai_san: 'TS-00133', ten_tai_san: 'Hồ Núi Cốc', loai: 'Hồ chứa', hanh_dong: 'Thêm mới', nguoi_cap_nhat: 'Nguyễn Văn B', ngay_cap_nhat: '07/06/2025 09:10' },
  { id: 6, ma_tai_san: 'TS-00130', ten_tai_san: 'Trạm bơm Thanh Hà', loai: 'Trạm bơm', hanh_dong: 'Xóa', nguoi_cap_nhat: 'Trần Thị C', ngay_cap_nhat: '06/06/2025 09:10' },
])
</script>
