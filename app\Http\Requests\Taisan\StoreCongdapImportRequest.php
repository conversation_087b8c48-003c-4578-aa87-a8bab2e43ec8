<?php

namespace App\Http\Requests\Taisan;

use Illuminate\Foundation\Http\FormRequest;

class StoreCongdapImportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // TODO: Implement proper authorization based on user permissions
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'mimes:xlsx,xls',
                'mimetypes:application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'max:10240', // 10MB in kilobytes
                function ($attribute, $value, $fail) {
                    // Additional security validation
                    if (!$value->isValid()) {
                        $fail('The uploaded file is corrupted or invalid.');
                    }
                    
                    // Check file extension matches MIME type
                    $extension = strtolower($value->getClientOriginalExtension());
                    $mimeType = $value->getMimeType();
                    
                    $validCombinations = [
                        'xlsx' => ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
                        'xls' => ['application/vnd.ms-excel', 'application/excel']
                    ];
                    
                    if (!isset($validCombinations[$extension]) || 
                        !in_array($mimeType, $validCombinations[$extension])) {
                        $fail('The file extension does not match the file content.');
                    }
                }
            ],
            'batch_size' => 'sometimes|integer|min:1|max:1000', // Optional batch size parameter
            'skip_errors' => 'sometimes|boolean', // Optional parameter to skip rows with errors
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'file.required' => 'Vui lòng chọn file để import.',
            'file.file' => 'Dữ liệu upload phải là file.',
            'file.mimes' => 'File phải có định dạng Excel (.xlsx hoặc .xls).',
            'file.mimetypes' => 'File phải có định dạng Excel hợp lệ.',
            'file.max' => 'Kích thước file không được vượt quá 10MB.',
            'batch_size.integer' => 'Kích thước batch phải là số nguyên.',
            'batch_size.min' => 'Kích thước batch phải ít nhất là 1.',
            'batch_size.max' => 'Kích thước batch không được vượt quá 1000.',
            'skip_errors.boolean' => 'Tham số skip_errors phải là true hoặc false.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'file' => 'file Excel',
            'batch_size' => 'kích thước batch',
            'skip_errors' => 'bỏ qua lỗi',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation after basic rules
            if ($this->hasFile('file')) {
                $file = $this->file('file');
                
                // Check if file is readable
                if (!is_readable($file->getPathname())) {
                    $validator->errors()->add('file', 'File không thể đọc được.');
                }
                
                // Additional security check for file content
                $fileContent = file_get_contents($file->getPathname(), false, null, 0, 1024);
                if ($fileContent === false) {
                    $validator->errors()->add('file', 'Không thể đọc nội dung file.');
                }
                
                // Check for Excel file signatures
                $excelSignatures = [
                    'xlsx' => ['504B0304'], // ZIP signature (XLSX is a ZIP file)
                    'xls' => ['D0CF11E0A1B11AE1', 'FDF5E4'] // OLE2 signature for XLS
                ];
                
                $fileHex = bin2hex(substr($fileContent, 0, 8));
                $extension = strtolower($file->getClientOriginalExtension());
                
                if (isset($excelSignatures[$extension])) {
                    $validSignature = false;
                    foreach ($excelSignatures[$extension] as $signature) {
                        if (strpos($fileHex, strtolower($signature)) === 0) {
                            $validSignature = true;
                            break;
                        }
                    }
                    
                    if (!$validSignature) {
                        $validator->errors()->add('file', 'File không phải là file Excel hợp lệ.');
                    }
                }
            }
        });
    }
}
