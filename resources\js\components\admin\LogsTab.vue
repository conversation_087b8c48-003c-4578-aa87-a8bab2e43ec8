<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Icon from '@/components/Icon.vue'

const activityLogs = ref([
  { time: '09/06/2025 10:25:36', user: '<PERSON>uy<PERSON>n <PERSON>', action: 'Đăng nhập', description: '<PERSON>ăng nhập thành công vào hệ thống', ip: '*************', device: 'Chrome 120.0 / Windows 10' },
  { time: '09/06/2025 10:30:15', user: '<PERSON>uy<PERSON><PERSON>n <PERSON>', action: 'Tạo mới', description: 'Thêm mới tài sản "Đập <PERSON><PERSON>" (TS-00131)', ip: '*************', device: 'Firefox 119.0 / Ubuntu 22.04' },
  { time: '09/06/2025 11:05:22', user: '<PERSON><PERSON><PERSON><PERSON>', action: '<PERSON><PERSON><PERSON> nhật', description: '<PERSON><PERSON><PERSON> nhật thông tin tài sản "Trạm bơm <PERSON>" (TS-00125)', ip: '*************', device: 'Edge 120.0 / Windows 11' },
  { time: '09/06/2025 11:30:45', user: 'Lê Văn D', action: 'Phê duyệt', description: 'Phê duyệt thêm mới tài sản "Đập Tuyển Quang" (TS-00131)', ip: '*************', device: 'Safari 17.0 / macOS 14.0' },
  { time: '09/06/2025 13:15:10', user: 'Phạm Thị E', action: 'Từ chối', description: 'Từ chối cập nhật thông tin tài sản "Kênh Đồng Cửu Long" (TS-00129)', ip: '*************', device: 'Chrome 120.0 / Android 14' },
  { time: '09/06/2025 14:20:30', user: 'Hoàng Văn F', action: 'Xóa', description: 'Xóa tài sản "Trạm bơm Thanh Hà" (TS-00128)', ip: '*************', device: 'Firefox 119.0 / Windows 10' },
  { time: '09/06/2025 15:45:12', user: 'Nguyễn Văn A', action: 'Xuất báo cáo', description: 'Xuất báo cáo danh mục TSKCHTLL Quý II/2025', ip: '*************', device: 'Chrome 120.0 / Windows 10' },
  { time: '09/06/2025 16:30:05', user: 'Trần Thị B', action: 'Đăng xuất', description: 'Đăng xuất khỏi hệ thống', ip: '*************', device: 'Firefox 119.0 / Ubuntu 22.04' },
])

const getActionClass = (action: string) => {
  switch (action) {
    case 'Đăng nhập':
    case 'Tạo mới':
    case 'Cập nhật':
    case 'Phê duyệt':
      return 'bg-blue-100 text-blue-800'
    case 'Từ chối':
    case 'Xóa':
      return 'bg-red-100 text-red-800'
    case 'Xuất báo cáo':
      return 'bg-purple-100 text-purple-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">
        Lịch Sử Hoạt Động
      </h2>
      <div class="flex items-center gap-2">
        <Button variant="outline">
          <Icon name="Download" class="h-4 w-4 mr-2" />
          Xuất Excel
        </Button>
        <Button>
          <Icon name="RefreshCw" class="h-4 w-4 mr-2" />
          Làm mới
        </Button>
      </div>
    </div>
    <div class="flex items-center gap-4 mb-4 p-4 bg-muted/50 rounded-lg">
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Từ ngày</label>
        <Input type="date" class="w-40" />
      </div>
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Đến ngày</label>
        <Input type="date" class="w-40" />
      </div>
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Người dùng</label>
        <select class="border rounded px-2 py-1.5 w-48">
          <option>Tất cả người dùng</option>
        </select>
      </div>
      <div class="grid gap-1.5">
        <label class="text-sm font-medium">Loại hoạt động</label>
        <select class="border rounded px-2 py-1.5 w-48">
          <option>Tất cả hoạt động</option>
        </select>
      </div>
      <div class="self-end">
        <Button>
          <Icon name="Filter" class="h-4 w-4 mr-2" />
          Lọc
        </Button>
      </div>
    </div>
    <div class="border rounded-md">
      <table class="w-full">
        <thead>
          <tr class="border-b">
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              THỜI GIAN
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              NGƯỜI DÙNG
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              HOẠT ĐỘNG
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              MÔ TẢ
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              ĐỊA CHỈ IP
            </th>
            <th class="p-3 text-left text-sm font-semibold text-muted-foreground">
              THIẾT BỊ
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(log, index) in activityLogs" :key="index" class="border-b last:border-b-0">
            <td class="p-3 text-sm">
              {{ log.time }}
            </td>
            <td class="p-3 font-medium">
              {{ log.user }}
            </td>
            <td class="p-3">
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getActionClass(log.action)"
              >
                {{ log.action }}
              </span>
            </td>
            <td class="p-3 text-sm">
              {{ log.description }}
            </td>
            <td class="p-3 text-sm">
              {{ log.ip }}
            </td>
            <td class="p-3 text-sm">
              {{ log.device }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="flex justify-between items-center mt-4 text-sm text-muted-foreground">
      <span>Hiển thị 1 đến 8 của 8 bản ghi</span>
    </div>
  </div>
</template>
