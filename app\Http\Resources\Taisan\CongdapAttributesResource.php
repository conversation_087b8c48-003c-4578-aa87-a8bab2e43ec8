<?php

namespace App\Http\Resources\Taisan;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CongdapAttributesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'id_qt' => $this->id_qt,
            'id_xa' => $this->id_xa,
            'tenxa' => $this->whenLoaded('xa', $this->xa->tenxa ?? null),
            'nguyengia' => $this->whenLoaded('quyettoan', $this->quyettoan->nguyengia ?? null),
            'ten' => $this->ten,
            'quymo_ct' => $this->quymo_ct,
            'loai_ct' => $this->loai_ct,
            'nam_xd' => $this->nam_xd,
            'nam_sd' => $this->nam_sd,
            'dt_dat' => (float) $this->dt_dat,
            'tinhtrang' => $this->tinhtrang,
            'quytrinh_vh' => $this->quytrinh_vh,
            'quytrinh_bt' => $this->quytrinh_bt,
            'dv_quanly' => $this->dv_quanly,
            'phuongthuc' => $this->phuongthuc,
            'chuthich' => $this->chuthich,
        ];
    }
}
