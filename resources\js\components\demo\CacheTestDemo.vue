<template>
  <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
    <h2 class="text-xl font-semibold mb-4">Demo Test Cache & Refresh System</h2>
    
    <!-- Status Display -->
    <div class="mb-4 p-4 bg-gray-50 rounded">
      <h3 class="font-medium mb-2">Trạng thái hệ thống:</h3>
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <span class="font-medium">Cache Driver:</span> {{ cacheDriver }}
        </div>
        <div>
          <span class="font-medium">Hỗ trợ Tags:</span> 
          <span :class="supportsTags ? 'text-green-600' : 'text-orange-600'">
            {{ supportsTags ? 'Có' : 'Không (dùng versioning)' }}
          </span>
        </div>
        <div>
          <span class="font-medium">Tổng records:</span> {{ totalRecords }}
        </div>
        <div>
          <span class="font-medium">Lần refresh cuối:</span> {{ lastRefresh }}
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mb-4 flex gap-2 flex-wrap">
      <button 
        @click="testCacheSpeed" 
        :disabled="isLoading"
        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
      >
        {{ isLoading ? 'Đang test...' : 'Test Tốc độ Cache' }}
      </button>
      
      <button 
        @click="clearCache" 
        :disabled="isLoading"
        class="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50"
      >
        Xóa Cache
      </button>
      
      <button 
        @click="triggerGlobalRefresh" 
        :disabled="isLoading"
        class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
      >
        Trigger Global Refresh
      </button>
      
      <button 
        @click="createTestRecord" 
        :disabled="isLoading"
        class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
      >
        Tạo Record Test
      </button>
    </div>

    <!-- Results Display -->
    <div v-if="testResults.length > 0" class="mb-4">
      <h3 class="font-medium mb-2">Kết quả test:</h3>
      <div class="bg-gray-50 p-3 rounded text-sm font-mono">
        <div v-for="(result, index) in testResults" :key="index" class="mb-1">
          {{ result }}
        </div>
      </div>
    </div>

    <!-- Recent Records -->
    <div v-if="recentRecords.length > 0">
      <h3 class="font-medium mb-2">Records gần đây ({{ recentRecords.length }}):</h3>
      <div class="overflow-x-auto">
        <table class="min-w-full text-sm">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-3 py-2 text-left">ID</th>
              <th class="px-3 py-2 text-left">Tên</th>
              <th class="px-3 py-2 text-left">Loại</th>
              <th class="px-3 py-2 text-left">Thời gian</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="record in recentRecords" :key="record.id" class="border-t">
              <td class="px-3 py-2">{{ record.id }}</td>
              <td class="px-3 py-2">{{ record.properties?.ten || 'N/A' }}</td>
              <td class="px-3 py-2">{{ record.properties?.loai_ct || 'N/A' }}</td>
              <td class="px-3 py-2">{{ formatTime(record.created_at) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import axios from 'axios'
import { useDataRefresh } from '@/composables/useDataRefresh'

// Composables
const { triggerRefresh, onRefresh } = useDataRefresh()

// State
const isLoading = ref(false)
const cacheDriver = ref('unknown')
const supportsTags = ref(false)
const totalRecords = ref(0)
const lastRefresh = ref('Chưa có')
const testResults = ref<string[]>([])
const recentRecords = ref<any[]>([])

// Test cache speed
const testCacheSpeed = async () => {
  isLoading.value = true
  testResults.value = []
  
  try {
    // First call (from database)
    const start1 = performance.now()
    const response1 = await axios.get('/api/taisan/congdap?per_page=10&_cache_bust=' + Date.now())
    const time1 = performance.now() - start1
    
    // Second call (from cache)
    const start2 = performance.now()
    const response2 = await axios.get('/api/taisan/congdap?per_page=10')
    const time2 = performance.now() - start2
    
    const speedup = time1 / time2
    
    testResults.value = [
      `Lần gọi đầu tiên (từ DB): ${time1.toFixed(2)}ms`,
      `Lần gọi thứ hai (từ cache): ${time2.toFixed(2)}ms`,
      `Tăng tốc: ${speedup.toFixed(2)}x`,
      `Records: ${response1.data.data.features.length}`
    ]
    
    recentRecords.value = response1.data.data.features.slice(0, 5)
    
  } catch (error) {
    testResults.value = [`Lỗi: ${error.message}`]
  } finally {
    isLoading.value = false
  }
}

// Clear cache
const clearCache = async () => {
  isLoading.value = true
  try {
    // Tạo một record dummy để trigger cache clear
    await axios.post('/api/taisan/congdap', {
      type: 'Feature',
      properties: {
        ten: 'Test Cache Clear - ' + Date.now(),
        loai_ct: 'TEST'
      },
      geometry: {
        type: 'Point',
        coordinates: [106.7, 10.7]
      }
    })
    
    // Xóa record vừa tạo
    testResults.value = ['Cache đã được xóa thông qua việc tạo/xóa record test']
    
  } catch (error) {
    testResults.value = [`Lỗi khi xóa cache: ${error.message}`]
  } finally {
    isLoading.value = false
  }
}

// Trigger global refresh
const triggerGlobalRefresh = () => {
  triggerRefresh()
  lastRefresh.value = new Date().toLocaleTimeString()
  testResults.value = ['Đã trigger global refresh cho tất cả components']
}

// Create test record
const createTestRecord = async () => {
  isLoading.value = true
  try {
    const testData = {
      type: 'Feature',
      properties: {
        ten: `Test Record ${Date.now()}`,
        quymo_ct: 'Test Scale',
        loai_ct: 'TEST',
        nam_xd: new Date().getFullYear(),
        dv_quanly: 'Test Unit',
        chuthich: 'Record được tạo từ demo component'
      },
      geometry: {
        type: 'Point',
        coordinates: [106.7 + Math.random() * 0.1, 10.7 + Math.random() * 0.1]
      }
    }

    const response = await axios.post('/api/taisan/congdap', testData)
    
    if (response.data.success) {
      testResults.value = [
        'Tạo record thành công!',
        `ID: ${response.data.data.id}`,
        `Tên: ${response.data.data.properties.ten}`,
        'Cache sẽ được refresh tự động'
      ]
      
      // Refresh data
      await loadSystemInfo()
    } else {
      testResults.value = [`Lỗi: ${response.data.message}`]
    }
    
  } catch (error) {
    testResults.value = [`Lỗi khi tạo record: ${error.message}`]
  } finally {
    isLoading.value = false
  }
}

// Load system info
const loadSystemInfo = async () => {
  try {
    const response = await axios.get('/api/taisan/congdap?per_page=1')
    if (response.data.success) {
      totalRecords.value = response.data.data.total || 0
    }
    
    // Get cache driver info (would need a dedicated endpoint in real app)
    cacheDriver.value = 'database' // From our test
    supportsTags.value = false
    
  } catch (error) {
    console.error('Error loading system info:', error)
  }
}

// Format time
const formatTime = (timestamp: string) => {
  if (!timestamp) return 'N/A'
  return new Date(timestamp).toLocaleString()
}

// Lifecycle
onMounted(async () => {
  await loadSystemInfo()
  
  // Register for global refresh
  const unregister = onRefresh(async () => {
    lastRefresh.value = new Date().toLocaleTimeString()
    await loadSystemInfo()
  })
  
  onUnmounted(() => {
    unregister()
  })
})
</script>
