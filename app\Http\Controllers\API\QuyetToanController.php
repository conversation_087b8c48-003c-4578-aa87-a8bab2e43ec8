<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class QuyetToanController extends Controller
{
    use ApiResponseTrait;

    public function getOptions()
    {
        try {
            $quyetToans = DB::table('taisan.quyettoan')
                ->select('id', 'nguyengia', 'qd_dautu', 'qd_quyettoan')
                ->where('id', 'like', 'CO%')
                ->orderBy('id', 'desc')
                ->get();

            return $this->successResponse($quyetToans);
        } catch (\Exception $e) {
            return $this->errorResponse('Không thể lấy danh sách quyết toán: ' . $e->getMessage(), 500);
        }
    }
}
