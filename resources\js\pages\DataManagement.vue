<template>
  <MainLayout>
    <template #sidebar>
      <div class="p-4">
        <label class="block mb-2 text-base">
          <select
            class="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer">
            <option value="" disabled>Chọn phường/xã</option>
            <option value="all">Tất cả</option>
            <option value="phuong1">Phường Tân Định</option>
            <option value="phuong2">Thị trấn Tân Túc</option>
            <option value="phuong3">Xã Phạm Văn Hai</option>
            <option value="phuong4">Xã Bình Lợi</option>
            <option value="phuong5">Xã Tân Nhựt</option>
            <option value="phuong5">Xã Tân Kiên</option>
            <option value="phuong5">Xã Phong Phú</option>
            <option value="phuong5">Xã An Phú Tây</option>
            <option value="phuong5">Xã <PERSON></option>
            <option value="phuong5">Xã Đa Phước</option>
            <option value="phuong5">Xã Tân Quý Tây</option>
            <option value="phuong5">Xã Quy Đức</option>
          </select>
        </label>
        <!-- Accordion Sidebar -->
        <div>
          <!-- Bản đồ chuyên đề -->
          <nav class="flex flex-col space-y-1">
            <a v-for="asset in assets" :key="asset.id" href="#"
              class="flex items-center gap-2 px-3 py-2 rounded-md transition-colors duration-200 ease-in-out text-sm"
              :class="[
                activeAsset === asset.id
                  ? 'bg-blue-50 text-blue-600 font-medium'
                  : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50',
              ]" @click.prevent="selectAsset(asset.id)">
              <Icon :name="asset.icon" class="h-5 w-5" />
              <span>{{ asset.name }}</span>
            </a>
          </nav>
        </div>
      </div>
    </template>

    <template #default="{ isSidebarOpen }">
      <div>
        <div class="flex items-center border-b">
          <button v-for="tab in tabs" :key="tab.name" @click="activeTab = tab.id"
            class="flex items-center px-3 py-2.5 -mb-px border-b-2 text-sm font-medium" :class="[
              tab.id === activeTab
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            ]">
            <Icon :name="tab.icon" class="mr-px h-5 w-5" />
            {{ tab.name }}
          </button>
        </div>

        <div class="mt-6">
          <component :is="currentComponent.component" v-bind="currentComponent.props"
            @refresh-data="handleRefreshData" />
        </div>
      </div>
    </template>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import axios from 'axios'
import MainLayout from '@/layouts/MainLayout.vue'
import Icon from '@/components/Icon.vue'
import EditInfoTab from '@/components/data/EditInfoTab.vue'
import ImportAssetTab from '@/components/data/ImportAssetTab.vue'
import ApproveDataTab from '@/components/data/ApproveDataTab.vue'
import CacheTestDemo from '@/components/demo/CacheTestDemo.vue'
import { useDataRefresh } from '@/composables/useDataRefresh'

// Sử dụng data refresh composable
const { triggerRefresh } = useDataRefresh()

// --- State Management ---
const activeTab = ref('edit')
const activeAsset = ref<string | null>(null)
const features = ref<any[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// --- Data Fetching ---
const fetchData = async () => {
  loading.value = true
  error.value = null
  features.value = []
  try {
    const response = await axios.get('/api/taisan/congdap?per_page=-1');
    if (response.data.success) {
      features.value = response.data.data.features;
    } else {
      throw new Error(response.data.message || 'Failed to fetch data.');
    }
  } catch (e: any) {
    error.value = e.message || 'An unknown error occurred.';
  } finally {
    loading.value = false;
  }
};

// Giữ lại alias cho tương thích ngược (có thể xóa sau khi cập nhật tất cả references)
// const fetchCongDapData = fetchData;

// --- Event Handlers ---
const handleRefreshData = async () => {
  // Tải lại dữ liệu sau khi có thay đổi từ component con
  if (activeAsset.value === 'dam') {
    await fetchData();
    // Trigger refresh cho các components khác (như Map.vue)
    triggerRefresh();
  }
};

// --- Sidebar Logic ---
const selectAsset = (assetType: string) => {
  activeAsset.value = assetType
  console.log(`Selected asset type: ${assetType}`)

  // Fetch data only if the selected asset is 'dam' (Cống đập)
  if (assetType === 'dam') {
    fetchData();
  } else {
    // Clear data for other asset types for now
    features.value = [];
    error.value = null;
    loading.value = false;
  }
}

// --- Tab Management ---
const tabs = [
  { id: 'edit', name: 'Chỉnh sửa thông tin', icon: 'pencil', component: EditInfoTab },
  { id: 'add', name: 'Nhập dữ liệu tài sản', icon: 'plus', component: ImportAssetTab },
  /* { id: 'approve', name: 'Phê duyệt dữ liệu', icon: 'check-circle', component: ApproveDataTab },
  { id: 'demo', name: 'Demo Cache System', icon: 'settings', component: CacheTestDemo } */
]

const assets = [
  { id: 'dam', name: 'Cống đập', icon: 'ToggleLeft' },
  { id: 'puhmp', name: 'Trạm bơm', icon: 'ToggleLeft' },
  { id: 'dike', name: 'Đê bao bờ bao', icon: 'ToggleLeft' },
  { id: 'canal', name: 'Kênh mương', icon: 'ToggleLeft' },
  { id: 'orther', name: 'Tài sản khác', icon: 'ToggleLeft' },
  { id: 'device', name: 'Thiết bị dụng cụ', icon: 'ToggleLeft' },
  { id: 'settlement', name: 'Quyết toán', icon: 'ToggleLeft' },
]

const currentComponent = computed(() => {
  const tab = tabs.find(t => t.id === activeTab.value)
  if (!tab) return { component: null, props: {} };

  // Pass props to the EditInfoTab component
  if (tab.id === 'edit') {
    return {
      component: tab.component,
      props: {
        features: features.value,
        loading: loading.value,
        error: error.value,
        assetName: activeAsset.value === 'dam' ? 'Cống Đập' : 'Tài sản'
      }
    };
  }

  return { component: tab.component, props: {} };
})
</script>

<style scoped>
/* Using Tailwind utilities, so scoped styles might not be needed as much */
/* You can add custom styles here if required */
</style>