<?php

namespace App\Exceptions\Taisan;

use Exception;

class CongdapNotFoundException extends Exception
{
    public function __construct(string $id)
    {
        parent::__construct("Không tìm thấy cống đập với ID: {$id}");
    }

    public function render()
    {
        return response()->json([
            'message' => $this->getMessage(),
            'code' => 'CONGDAP_NOT_FOUND'
        ], 404);
    }
}