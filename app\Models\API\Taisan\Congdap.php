<?php

namespace App\Models\API\Taisan;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Models\API\Basemap\Rgxa;      // <<< Đảm bảo namespace và tên class đúng
use App\Models\API\Taisan\Quyettoan; // <<< Đảm bảo namespace và tên class đúng


class Congdap extends Model
{
    use HasFactory;
    protected $connection = 'pgsql';
    protected $table = 'taisan.congdap';
    protected $primaryKey = 'id';
    public $timestamps = false;
    public $incrementing = false; // Khóa chính không tự tăng
    protected $keyType = 'string'; // Kiểu dữ liệu khóa chính là chuỗi
    protected $fillable = [
        'id_qt',
        'id_xa',
        'ten',
        'quymo_ct',
        'loai_ct',
        'nam_xd',
        'nam_sd',
        'dt_dat',
        'tinhtrang',
        'quytrinh_vh',
        'quytrinh_bt',
        'dv_quanly',
        'phuongthuc',
        'chuthich',
        'geom',
    ];

    // --- THÊM PHẦN TỰ ĐỘNG TẠO ID ---
    protected static function booted()
    {
        static::creating(function ($congdap) {
            // Chỉ tạo ID nếu nó chưa được gán
            if (empty($congdap->{$congdap->getKeyName()})) { // Dùng getKeyName() để linh hoạt hơn
                // Tìm ID lớn nhất hiện có theo dạng 'Cxxxx'
                // Sử dụng DB facade vì model có thể chưa được khởi tạo hoàn chỉnh
                $latestCongdap = DB::connection($congdap->getConnectionName()) // Chỉ định connection
                                    ->table($congdap->getTable()) // Chỉ định table
                                    ->where($congdap->getKeyName(), 'like', 'C%')
                                    // Sắp xếp theo phần số của ID giảm dần (PostgreSQL dùng SUBSTRING)
                                    ->orderByRaw("CAST(SUBSTRING({$congdap->getKeyName()} from 2) AS INTEGER) DESC")
                                    ->lockForUpdate() // Khóa để tránh race condition
                                    ->first();

                $nextNumericId = 1; // Số bắt đầu nếu chưa có bản ghi nào
                if ($latestCongdap) {
                    // Lấy phần số từ ID cuối cùng và tăng lên 1
                    // Dùng $congdap->getKeyName() thay vì 'id' cứng
                    $lastNumericId = (int) substr($latestCongdap->{$congdap->getKeyName()}, 1);
                    $nextNumericId = $lastNumericId + 1;
                }

                // Định dạng ID mới thành 'Cxxxx' (4 chữ số, có số 0 đằng trước)
                // Dùng $congdap->getKeyName()
                $congdap->{$congdap->getKeyName()} = sprintf("C%04d", $nextNumericId);
            }
        });
    }
    // --- KẾT THÚC PHẦN TỰ ĐỘNG TẠO ID ---

    public static function getGeoJSON()
    {
        $query = DB::connection('pgsql')->table('taisan.congdap')
            ->selectRaw('id, ST_AsGeoJSON(geom) as geom, id_qt, id_xa, ten, quymo_ct, loai_ct, nam_xd, nam_sd, dt_dat, tinhtrang, quytrinh_vh, quytrinh_bt, dv_quanly, phuongthuc, chuthich')
            ->get();

        $features = $query->map(function ($item) {
            return [
                'type' => 'Feature',
                'geometry' => json_decode($item->geom),
                'properties' => [
                    'id' => (string) $item->id,
                    'id_qt' => (string) $item->id_qt,
                    'id_xa' => (string) $item->id_xa,
                    'ten' => (string) $item->ten,
                    'quymo_ct' => (string) $item->quymo_ct,
                    'loai_ct' => (string) $item->loai_ct,
                    'nam_xd' => (int) $item->nam_xd,
                    'nam_sd' => (int) $item->nam_sd,
                    'dt_dat' => (float) $item->dt_dat,
                    'tinhtrang' => (string) $item->tinhtrang,
                    'quytrinh_vh' => (string) $item->quytrinh_vh,
                    'quytrinh_bt' => (string) $item->quytrinh_bt,
                    'dv_quanly' => (string) $item->dv_quanly,
                    'phuongthuc' => (string) $item->phuongthuc,
                    'chuthich' => (string) $item->chuthich,

                ],
            ];
        });

        return response()->json([
            'type' => 'FeatureCollection',
            'features' => $features,
        ]);
    }

    public static function findAsGeoJSON(string  $id)
    {
        $item = DB::connection('pgsql')->table('taisan.congdap')
             ->selectRaw('
                id,
                ST_AsGeoJSON(geom) as geom_geojson,
                id_qt,
                id_xa,
                ten,
                quymo_ct,
                loai_ct,
                nam_xd,
                nam_sd,
                dt_dat,
                tinhtrang,
                quytrinh_vh,
                quytrinh_bt,
                dv_quanly,
                phuongthuc,
                chuthich
             ')
             ->where('id', $id)
             ->first();

        if (!$item) {
            return null;
        }
        return [
            'type' => 'Feature',
            'geometry' => json_decode($item->geom_geojson),
            'properties' => [
                'id' => (string) $item->id,
                'id_qt' => (string) $item->id_qt,
                'id_xa' => (string) $item->id_xa,
                'ten' => (string) $item->ten,
                'quymo_ct' => (string) $item->quymo_ct,
                'loai_ct' => (string) $item->loai_ct,
                'nam_xd' => (int) $item->nam_xd,
                'nam_sd' => (int) $item->nam_sd,
                'dt_dat' => (float) $item->dt_dat,
                'tinhtrang' => (string) $item->tinhtrang,
                'quytrinh_vh' => (string) $item->quytrinh_vh,
                'quytrinh_bt' => (string) $item->quytrinh_bt,
                'dv_quanly' => (string) $item->dv_quanly,
                'phuongthuc' => (string) $item->phuongthuc,
                'chuthich' => (string) $item->chuthich,
            ],
        ];
    }

    public static function geometryFromGeoJSON(string $geojsonString)
    {
        // Escape dấu nháy đơn cho SQL
        $escaped = str_replace("'", "''", $geojsonString);

        // Trả về duy nhất một expression, không thừa dấu phẩy
        return DB::raw("ST_SetSRID(ST_GeomFromGeoJSON('{$escaped}'), 4326)");
    }

    // --- <<< THÊM: Định nghĩa Relationships >>> ---

    /**
     * Lấy thông tin Xã (rg_xa) liên quan đến Cống đập.
     */
    public function xa()
    {
        // belongsTo(RelatedModel, foreign_key, owner_key)
        // foreign_key: Khóa ngoại trong bảng 'taisan.congdap' (là id_xa)
        // owner_key: Khóa chính trong bảng 'basemap.rg_xa' (là 'id')
        return $this->belongsTo(Rgxa::class, 'id_xa', 'id');
    }

    /**
     * Lấy thông tin Quyết toán (quyettoan) liên quan đến Cống đập.
     */
    public function quyettoan()
    {
        // belongsTo(RelatedModel, foreign_key, owner_key)
        // foreign_key: Khóa ngoại trong bảng 'taisan.congdap' (là id_qt)
        // owner_key: Khóa chính trong bảng 'taisan.quyettoan' (là 'id')
        return $this->belongsTo(Quyettoan::class, 'id_qt', 'id');
    }
    // --- <<< KẾT THÚC: Định nghĩa Relationships >>> ---
}