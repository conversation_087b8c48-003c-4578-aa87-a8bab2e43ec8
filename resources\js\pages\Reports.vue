<template>
  <MainLayout>
    <template #sidebar>
      <div class="p-4">
        <nav class="flex flex-col space-y-1">
          <a
            v-for="tab in tabs"
            :key="tab.id"
            href="#"
            class="flex items-center gap-2 px-3 py-2 rounded-md transition-colors duration-200 ease-in-out text-sm"
            :class="[
              activeTab === tab.id
                ? 'bg-blue-50 text-blue-600 font-medium'
                : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50',
            ]"
            @click.prevent="activeTab = tab.id"
          >
            <Icon :name="tab.icon" class="h-5 w-5" />
            <span>{{ tab.name }}</span>
          </a>
        </nav>
      </div>
    </template>

    <div class="h-full flex flex-col">
      <!-- View xuất báo cáo thống kê -->
      <ExportReport v-if="activeTab === 'export'" />
      <ReportStats v-else-if="activeTab === 'stats'" reportTypeName="<PERSON>iểu đồ thống kê tài sản" />
      <!-- View tính giá trị hao mòn -->
      <DepreciationCalculator v-else-if="activeTab === 'depreciation'" />
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MainLayout from '@/layouts/MainLayout.vue'
import Icon from '@/components/Icon.vue'
import ExportReport from '@/components/reports/ExportReport.vue'
import DepreciationCalculator from '@/components/reports/DepreciationCalculator.vue'
import ReportStats from '@/components/reports/ReportStats.vue'

const activeTab = ref('export')

const tabs = [
  { id: 'export', name: 'Xuất báo cáo theo mẫu', icon: 'ClipboardPaste' },
  { id: 'stats', name: 'Tạo biểu đồ thống kê', icon: 'ChartPie' },
  { id: 'depreciation', name: 'Tính giá trị hao mòn', icon: 'Calculator' },
]
</script>