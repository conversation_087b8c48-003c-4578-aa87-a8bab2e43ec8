<script setup lang="ts">
import { ref } from 'vue'
import MainLayout from '@/layouts/MainLayout.vue'
import Icon from '@/components/Icon.vue'
import UsersTab from '@/components/admin/UsersTab.vue'
import RolesTab from '@/components/admin/RolesTab.vue'
import LogsTab from '@/components/admin/LogsTab.vue'
import SettingsTab from '@/components/admin/SettingsTab.vue'

const activeTab = ref('users')

const tabs = [
  { id: 'users', name: 'Ngư<PERSON>i dùng', icon: 'Users' },
  { id: 'roles', name: '<PERSON><PERSON> quyền', icon: 'ShieldCheck' },
  { id: 'logs', name: '<PERSON><PERSON><PERSON> sử hoạt động', icon: 'History' },
  { id: 'settings', name: '<PERSON><PERSON>i đặt', icon: 'Settings' },
]
</script>

<template>
  <MainLayout>
    <template #sidebar>
      <div class="p-4">
        <nav class="flex flex-col space-y-1">
          <a
            v-for="tab in tabs"
            :key="tab.id"
            href="#"
            class="flex items-center gap-2 px-3 py-2 rounded-md transition-colors duration-200 ease-in-out text-sm"
            :class="[
              activeTab === tab.id
                ? 'bg-blue-50 text-blue-600 font-medium'
                : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50',
            ]"
            @click.prevent="activeTab = tab.id"
          >
            <Icon :name="tab.icon" class="h-5 w-5" />
            <span>{{ tab.name }}</span>
          </a>
        </nav>
      </div>
    </template>
    <template #default="{ isSidebarOpen }">
      <div class="px-4 sm:px-6 flex flex-col h-full">
        <div>
          <UsersTab v-if="activeTab === 'users'" />
          <RolesTab v-if="activeTab === 'roles'" />
          <LogsTab v-if="activeTab === 'logs'" />
          <SettingsTab v-if="activeTab === 'settings'" />
        </div>
      </div>
    </template>
  </MainLayout>
</template>