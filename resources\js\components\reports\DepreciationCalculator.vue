<template>
  <div class="depreciation-calculator">
    <div class="calculator-header">
      <h2>Tính giá trị hao mòn</h2>
      <p class="description">Tính toán giá trị hao mòn của tài sản kết cấu hạ tầng thủy lợi</p>
    </div>

    <div class="calculator-form">
      <div class="form-row">
        <div class="form-group">
          <label>Loại tài sản:</label>
          <select v-model="assetType" class="form-select">
            <option value="">-- Chọn loại tài sản --</option>
            <option value="dam"><PERSON><PERSON><PERSON>, hồ chứa nước</option>
            <option value="canal">Kênh, mương, rạch</option>
            <option value="station">Trạm bơm</option>
            <option value="gate"><PERSON>ố<PERSON>, đập dâng</option>
            <option value="building">N<PERSON><PERSON>, công tr<PERSON>nh kiến trúc</option>
          </select>
        </div>
        <div class="form-group">
          <label>Năm đưa vào sử dụng:</label>
          <input type="number" v-model="startYear" class="form-input" min="1900" :max="currentYear" />
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Nguyên giá (VNĐ):</label>
          <input type="number" v-model="originalPrice" class="form-input" min="0" step="1000000" />
        </div>
        <div class="form-group">
          <label>Tỷ lệ hao mòn hàng năm (%):</label>
          <input type="number" v-model="depreciationRate" class="form-input" min="0" max="100" step="0.1" />
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Đã sửa chữa lớn:</label>
          <div class="checkbox-group">
            <input type="checkbox" id="majorRepair" v-model="hasMajorRepair" />
            <label for="majorRepair">Có</label>
          </div>
        </div>
        <div class="form-group" v-if="hasMajorRepair">
          <label>Năm sửa chữa gần nhất:</label>
          <input type="number" v-model="lastRepairYear" class="form-input" :min="startYear" :max="currentYear" />
        </div>
      </div>

      <div class="form-row" v-if="hasMajorRepair">
        <div class="form-group">
          <label>Chi phí sửa chữa (VNĐ):</label>
          <input type="number" v-model="repairCost" class="form-input" min="0" step="1000000" />
        </div>
        <div class="form-group">
          <label>Tỷ lệ khôi phục (%):</label>
          <input type="number" v-model="recoveryRate" class="form-input" min="0" max="100" step="0.1" />
        </div>
      </div>

      <div class="form-actions">
        <button @click="calculateDepreciation" class="action-btn primary">Tính toán</button>
        <button @click="resetForm" class="action-btn">Làm mới</button>
      </div>
    </div>

    <div v-if="calculationDone" class="calculation-results">
      <h3>Kết quả tính toán</h3>
      
      <div class="results-grid">
        <div class="result-card">
          <div class="result-title">Nguyên giá</div>
          <div class="result-value">{{ formatCurrency(originalPrice) }}</div>
        </div>
        
        <div class="result-card">
          <div class="result-title">Số năm sử dụng</div>
          <div class="result-value">{{ usageYears }} năm</div>
        </div>
        
        <div class="result-card">
          <div class="result-title">Tỷ lệ hao mòn</div>
          <div class="result-value">{{ depreciationRate }}%</div>
        </div>
        
        <div class="result-card">
          <div class="result-title">Giá trị hao mòn</div>
          <div class="result-value">{{ formatCurrency(depreciationValue) }}</div>
        </div>
        
        <div class="result-card highlight">
          <div class="result-title">Giá trị còn lại</div>
          <div class="result-value">{{ formatCurrency(remainingValue) }}</div>
        </div>
        
        <div class="result-card">
          <div class="result-title">Tỷ lệ còn lại</div>
          <div class="result-value">{{ remainingPercentage.toFixed(2) }}%</div>
        </div>
      </div>

      <div class="depreciation-chart">
        <div class="chart-title">Biểu đồ hao mòn tài sản</div>
        <div class="chart-container">
          <div class="chart-bar">
            <div class="chart-fill" :style="{ width: `${remainingPercentage}%` }"></div>
          </div>
          <div class="chart-labels">
            <span>0%</span>
            <span>25%</span>
            <span>50%</span>
            <span>75%</span>
            <span>100%</span>
          </div>
        </div>
      </div>

      <div class="calculation-notes">
        <h4>Ghi chú:</h4>
        <ul>
          <li>Giá trị hao mòn = Nguyên giá × Tỷ lệ hao mòn × Số năm sử dụng</li>
          <li>Nếu có sửa chữa lớn, giá trị còn lại sẽ được điều chỉnh theo tỷ lệ khôi phục</li>
          <li>Tài sản được coi là hết khấu hao khi giá trị còn lại dưới 10% nguyên giá</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Các biến trạng thái
const assetType = ref('')
const startYear = ref(2010)
const originalPrice = ref(1000000000)
const depreciationRate = ref(2.5)
const hasMajorRepair = ref(false)
const lastRepairYear = ref(2020)
const repairCost = ref(200000000)
const recoveryRate = ref(30)
const calculationDone = ref(false)

// Các giá trị tính toán
const depreciationValue = ref(0)
const remainingValue = ref(0)
const remainingPercentage = ref(0)

// Lấy năm hiện tại
const currentYear = new Date().getFullYear()

// Tính số năm sử dụng
const usageYears = computed(() => {
  return currentYear - startYear.value
})

// Hàm tính toán giá trị hao mòn
function calculateDepreciation() {
  // Tính giá trị hao mòn cơ bản
  const basicDepreciation = originalPrice.value * (depreciationRate.value / 100) * usageYears.value
  
  // Điều chỉnh nếu có sửa chữa lớn
  if (hasMajorRepair.value) {
    // Tính hao mòn trước khi sửa chữa
    const yearBeforeRepair = lastRepairYear.value - startYear.value
    const depreciationBeforeRepair = originalPrice.value * (depreciationRate.value / 100) * yearBeforeRepair
    
    // Tính hao mòn sau khi sửa chữa
    const yearsAfterRepair = currentYear - lastRepairYear.value
    const valueAfterRepair = originalPrice.value - depreciationBeforeRepair + (repairCost.value * (recoveryRate.value / 100))
    const depreciationAfterRepair = valueAfterRepair * (depreciationRate.value / 100) * yearsAfterRepair
    
    // Tổng giá trị hao mòn
    depreciationValue.value = depreciationBeforeRepair + depreciationAfterRepair
  } else {
    depreciationValue.value = basicDepreciation
  }
  
  // Đảm bảo giá trị hao mòn không vượt quá nguyên giá
  depreciationValue.value = Math.min(depreciationValue.value, originalPrice.value)
  
  // Tính giá trị còn lại
  remainingValue.value = originalPrice.value - depreciationValue.value
  
  // Tính tỷ lệ còn lại
  remainingPercentage.value = (remainingValue.value / originalPrice.value) * 100
  
  calculationDone.value = true
}

// Hàm reset form
function resetForm() {
  assetType.value = ''
  startYear.value = 2010
  originalPrice.value = 1000000000
  depreciationRate.value = 2.5
  hasMajorRepair.value = false
  lastRepairYear.value = 2020
  repairCost.value = 200000000
  recoveryRate.value = 30
  calculationDone.value = false
}

// Hàm định dạng tiền tệ
function formatCurrency(value: number) {
  return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value)
}
</script>

<style scoped>
.depreciation-calculator {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.calculator-header {
  margin-bottom: 1rem;
}

.calculator-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.description {
  color: #666;
}

.calculator-form {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.form-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #555;
}

.form-select, .form-input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.action-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.action-btn.primary {
  background-color: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.action-btn:hover {
  opacity: 0.9;
}

.calculation-results {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.calculation-results h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #333;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.result-card {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  text-align: center;
}

.result-card.highlight {
  background: #4f46e5;
  color: white;
}

.result-title {
  font-size: 0.9rem;
  color: inherit;
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.result-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: inherit;
}

.depreciation-chart {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.chart-title {
  font-weight: 500;
  margin-bottom: 1rem;
}

.chart-container {
  margin-bottom: 0.5rem;
}

.chart-bar {
  height: 24px;
  background: #f1f1f1;
  border-radius: 12px;
  overflow: hidden;
}

.chart-fill {
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #818cf8);
  border-radius: 12px;
  transition: width 0.5s ease-out;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #666;
}

.calculation-notes {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.calculation-notes h4 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.calculation-notes ul {
  padding-left: 1.5rem;
  color: #666;
}

.calculation-notes li {
  margin-bottom: 0.25rem;
}
</style>
