<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { AgGridVue } from 'ag-grid-vue3'
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community'
import 'ag-grid-community/styles/ag-theme-alpine.css'

// Đăng ký module ClientSideRowModel để khắc phục lỗi
ModuleRegistry.registerModules([AllCommunityModule])

// Định nghĩa props
interface Props {
  columnDefs?: any[]
  rowData?: any[]
  defaultColDef?: any
  gridOptions?: any
  theme?: string
  height?: string
  width?: string
  locale?: string
  maxHeight?: string
}

const props = withDefaults(defineProps<Props>(), {
  columnDefs: () => [],
  rowData: () => [],
  defaultColDef: () => ({
    minWidth: 100,
    resizable: true,
    filter: false,
    sortable: true
  }),
  gridOptions: () => ({}),
  theme: 'ag-theme-alpine',
  height: '100%',
  width: '100%',
  locale: 'vi-VN',
  maxHeight: '22vh'
})

// Hợp nhất các tùy chọn grid mặc định với các tùy chọn được truyền vào từ props
const mergedGridOptions = computed(() => ({
    pagination: true,
    paginationPageSize: 10,
    paginationPageSizeSelector: [10, 20, 50, 100],
    domLayout: 'autoHeight',
    animateRows: true,
    rowSelection: 'multiple',
    suppressRowClickSelection: true,
    rowHeight: 32, // Tăng nhẹ chiều cao mặc định của dòng
    headerHeight: 30, // Giảm chiều cao của header
    suppressPaginationPanel: true, // Tắt panel phân trang mặc định để sử dụng panel tùy chỉnh
    ...props.gridOptions // Ghi đè bằng các tùy chọn từ props
}));

// Định nghĩa emits
const emit = defineEmits(['rowClicked', 'rowSelected', 'cellClicked', 'gridReady', 'filterChanged'])

// Tham chiếu đến grid
const gridApi = ref<any>(null)
const columnApi = ref<any>(null)

// Dữ liệu nội bộ
const internalRowData = ref(props.rowData)

// Theo dõi thay đổi rowData từ props
watch(() => props.rowData, (newVal) => {
  internalRowData.value = newVal
}, { deep: true })

// Cấu hình ngôn ngữ tiếng Việt
const localeText = computed((): { [key: string]: string } => {
  if (props.locale === 'vi-VN') {
    return {
      // Phân trang
      page: 'Trang',
      more: 'Thêm',
      to: 'đến',
      of: 'của',
      next: 'Tiếp',
      last: 'Cuối',
      first: 'Đầu',
      previous: 'Trước',
      loadingOoo: 'Đang tải...',
      pageSizeSelectorLabel: 'Kích thước trang:',

      // Lọc
      selectAll: 'Chọn tất cả',
      searchOoo: 'Tìm kiếm...',
      blanks: 'Để trống',
      filterOoo: '',
      applyFilter: 'Áp dụng',
      equals: 'Bằng',
      notEqual: 'Không bằng',
      lessThan: 'Nhỏ hơn',
      greaterThan: 'Lớn hơn',
      lessThanOrEqual: 'Nhỏ hơn hoặc bằng',
      greaterThanOrEqual: 'Lớn hơn hoặc bằng',
      inRange: 'Trong khoảng',
      contains: 'Chứa',
      notContains: 'Không chứa',
      startsWith: 'Bắt đầu với',
      endsWith: 'Kết thúc với',

      // Các thông báo khác
      noRowsToShow: 'Không có dữ liệu',

      // Tiêu đề menu
      pinColumn: 'Ghim cột',
      pinLeft: 'Ghim trái',
      pinRight: 'Ghim phải',
      noPin: 'Bỏ ghim',
      valueAggregation: 'Tổng hợp giá trị',
      autosizeThiscolumn: 'Tự động điều chỉnh cột này',
      autosizeAllColumns: 'Tự động điều chỉnh tất cả các cột',
      groupBy: 'Nhóm theo',
      ungroupBy: 'Bỏ nhóm theo',
      resetColumns: 'Đặt lại cột',
      expandAll: 'Mở rộng tất cả',
      collapseAll: 'Thu gọn tất cả',
      copy: 'Sao chép',
      ctrlC: 'Ctrl+C',
      copyWithHeaders: 'Sao chép với tiêu đề',
      paste: 'Dán',
      ctrlV: 'Ctrl+V',
      export: 'Xuất',
      csvExport: 'Xuất CSV',
      excelExport: 'Xuất Excel'
    }
  }
  return {}
})

// Xử lý sự kiện grid ready
const onGridReady = (params: any) => {
  gridApi.value = params.api
  columnApi.value = params.columnApi
  emit('gridReady', { api: gridApi.value, columnApi: columnApi.value })
  // Tự động điều chỉnh chiều rộng tất cả các cột để vừa với nội dung
  if (columnApi.value) {
    columnApi.value.autoSizeAllColumns(false); // false để tính cả chiều rộng của header
  }
}

// Xử lý sự kiện row clicked
const onRowClicked = (event: any) => {
  emit('rowClicked', event)
}

// Xử lý sự kiện cell clicked
const onCellClicked = (event: any) => {
  emit('cellClicked', event)
}

// Xử lý sự kiện selection changed
const onSelectionChanged = () => {
  const selectedRows = gridApi.value.getSelectedRows()
  emit('rowSelected', selectedRows)
}

// Xử lý sự kiện filter changed
const onFilterChanged = (event: any) => {
  emit('filterChanged', event)
}

// Phương thức public để tìm kiếm
const searchData = (searchValue: string) => {
  if (!gridApi.value) return

  gridApi.value.setQuickFilter(searchValue)
}

// Phương thức public để xuất dữ liệu
const exportToCSV = (fileName: string = 'export.csv') => {
  if (!gridApi.value) return

  gridApi.value.exportDataAsCsv({
    fileName,
    processCellCallback: (params: any) => {
      // Xử lý dữ liệu trước khi xuất nếu cần
      return params.value
    }
  })
}

// Phương thức public để làm mới dữ liệu
const refreshData = () => {
  if (!gridApi.value) return

  gridApi.value.refreshCells({ force: true })
}

// Phương thức public để lấy dữ liệu đã lọc
const getFilteredData = () => {
  if (!gridApi.value) return []

  const filteredData: any[] = []
  gridApi.value.forEachNodeAfterFilter((node: any) => {
    filteredData.push(node.data)
  })

  return filteredData
}

// Biến cho phân trang tùy chỉnh
const currentPage = ref(1)
const totalPages = ref(1)
const totalRows = ref(0)
const pageSize = ref(10)

// Cập nhật thông tin phân trang
const updatePaginationState = () => {
  if (!gridApi.value) return

  const paginationProxy = gridApi.value.paginationGetPageSize()
  if (paginationProxy) {
    pageSize.value = gridApi.value.paginationGetPageSize()
  }

  currentPage.value = gridApi.value.paginationGetCurrentPage() + 1 // API trả về zero-based index
  const rowCount = gridApi.value.paginationGetRowCount()
  totalRows.value = rowCount
  totalPages.value = Math.ceil(rowCount / pageSize.value)
}

// Xử lý khi thay đổi trang
const onPageChange = (newPage: number) => {
  if (!gridApi.value) return

  gridApi.value.paginationGoToPage(newPage - 1) // API nhận zero-based index
  currentPage.value = newPage
}

// Xử lý khi thay đổi kích thước trang
const onPageSizeChange = (event: any) => {
  if (!gridApi.value) return

  // Nếu event là số, sử dụng trực tiếp, nếu không thì lấy từ v-model
  const newSize = typeof event === 'number' ? event : pageSize.value

  gridApi.value.paginationSetPageSize(newSize)
  updatePaginationState()
}

// Các hàm chuyển trang đã được xử lý trực tiếp trong template

// Theo dõi thay đổi của grid để cập nhật phân trang
watch(gridApi, () => {
  if (gridApi.value) {
    gridApi.value.addEventListener('paginationChanged', updatePaginationState)
    updatePaginationState()
  }
})

// Expose public methods
defineExpose({
  gridApi,
  columnApi,
  searchData,
  exportToCSV,
  refreshData,
  getFilteredData
})


</script>

<template>
  <div class="flex flex-col min-h-[100px] rounded border border-gray-300">
    <!-- Grid container with scrollable content -->
    <div class="flex flex-col min-h-[100px] overflow-y-auto" :style="{ maxHeight: props.maxHeight }">
      <div :class="props.theme" :style="{ height: props.height, width: props.width }">
        <ag-grid-vue
          :columnDefs="columnDefs"
          :rowData="internalRowData" 
          :defaultColDef="props.defaultColDef"
          :gridOptions="mergedGridOptions"
          :localeText="localeText"
          @grid-ready="onGridReady"
          @row-clicked="onRowClicked"
          @cell-clicked="onCellClicked"
          @selection-changed="onSelectionChanged"
          @filter-changed="onFilterChanged"
        >
        </ag-grid-vue>
      </div>
    </div>

    <!-- Custom pagination controls outside the scrollable area -->
    <div class="flex justify-between items-center px-4 py-1 bg-gray-100 border-t border-gray-300 text-xs">
      <div class="flex gap-4 text-gray-600">
        <span>{{ totalRows }} kết quả</span>
        <span>Trang {{ currentPage }}/{{ totalPages }}</span>
      </div>

      <div class="flex gap-1">
        <button
          class="flex items-center justify-center w-7 h-7 border border-gray-300 bg-white rounded cursor-pointer transition-all duration-200 hover:bg-gray-200 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
          @click="onPageChange(1)"
          :disabled="currentPage === 1"
        >
          <span>«</span>
        </button>

        <button
          class="flex items-center justify-center w-7 h-7 border border-gray-300 bg-white rounded cursor-pointer transition-all duration-200 hover:bg-gray-200 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
          @click="onPageChange(currentPage - 1)"
          :disabled="currentPage === 1"
        >
          <span>‹</span>
        </button>

        <button
          class="flex items-center justify-center w-7 h-7 border border-gray-300 bg-white rounded cursor-pointer transition-all duration-200 hover:bg-gray-200 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
          @click="onPageChange(currentPage + 1)"
          :disabled="currentPage === totalPages"
        >
          <span>›</span>
        </button>

        <button
          class="flex items-center justify-center w-7 h-7 border border-gray-300 bg-white rounded cursor-pointer transition-all duration-200 hover:bg-gray-200 hover:border-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
          @click="onPageChange(totalPages)"
          :disabled="currentPage === totalPages"
        >
          <span>»</span>
        </button>
      </div>

      <div class="flex items-center gap-2">
        <span>Hiển thị</span>
        <select v-model="pageSize" @change="onPageSizeChange" class="px-2 py-1 border border-gray-300 rounded bg-white">
          <option v-for="size in [10, 20, 50, 100]" :key="size" :value="size">{{ size }}</option>
        </select>
        <span>dòng</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Theme tối */
.ag-theme-alpine-dark {
  --ag-header-height: 50px;
  --ag-header-foreground-color: #fff;
  --ag-header-background-color: #343a40;
  --ag-odd-row-background-color: #343a40;
  --ag-header-cell-hover-background-color: #495057;
  --ag-row-hover-color: #495057;
  --ag-background-color: #212529;
  --ag-foreground-color: #fff;
  --ag-border-color: #495057;
}

/* Theme tùy chỉnh cho TSKCHTL */
.ag-theme-tskchtl {
  --ag-header-height: 50px;
  --ag-header-foreground-color: #fff;
  --ag-header-background-color: #1976d2;
  --ag-odd-row-background-color: #f5f5f5;
  --ag-header-cell-hover-background-color: #1565c0;
  --ag-row-hover-color: #e3f2fd;
  --ag-background-color: #fff;
  --ag-foreground-color: #333;
  --ag-border-color: #e0e0e0;
  --ag-secondary-border-color: #e0e0e0;
  --ag-row-border-color: #e0e0e0;
  --ag-cell-horizontal-border: solid 1px var(--ag-border-color);
}
</style>