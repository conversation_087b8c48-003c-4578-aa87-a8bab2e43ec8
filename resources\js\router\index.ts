import { createRouter, createWebHistory } from 'vue-router'
import Map from '@/pages/Map.vue'
import DataManagement from '@/pages/DataManagement.vue'
import Reports from '@/pages/Reports.vue'
import Admin from '@/pages/Admin.vue'

const routes = [
 /*  {
    path: '/',
    redirect: '/map'
  }, */
  {
    path: '/',
    name: 'map',
    component: Map
  },
  {
    path: '/data-management',
    name: 'data-management',
    component: DataManagement
  },
  {
    path: '/reports',
    name: 'reports',
    component: Reports
  },
  {
    path: '/admin',
    name: 'admin',
    component: Admin
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router 