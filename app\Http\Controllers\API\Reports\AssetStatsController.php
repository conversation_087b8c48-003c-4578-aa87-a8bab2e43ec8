<?php

namespace App\Http\Controllers\API\Reports;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssetStatsController extends Controller
{
    use ApiResponseTrait;

    public function getStats(Request $request)
    {
        try {
            // Validate input parameters
            $validatedData = $request->validate([
                'xa_phuong' => 'nullable|string',
                'quan_huyen' => 'nullable|string',
                'loai_tai_san' => 'nullable|string',
                'tinh_trang' => 'nullable|string',
            ]);

            $currentYear = Carbon::now()->year;

            // Base query for each asset type with common fields
            $commonSelect = "
                ten as name,
                quymo_ct as quy_mo,
                loai_ct as loai_cong_trinh,
                nam_sd as start_date,
                dt_dat as dien_tich_dat,
                COALESCE(q.nguyengia, 0) as original_price,
                tinhtrang as tinh_trang,
                chuthich as ghi_chu,
                r.tenxa as unit,
                CASE 
                    WHEN tinhtrang ILIKE '%hư hỏng%' THEN false
                    ELSE true -- <PERSON><PERSON><PERSON> đ<PERSON>nh null hoặc các giá trị khác là đang hoạt động
                END as hoat_dong,
                CASE 
                    WHEN nam_sd IS NOT NULL AND nam_sd <> 0 AND q.nguyengia IS NOT NULL THEN 
                        GREATEST(0, q.nguyengia - ($currentYear - nam_sd::integer) * 0.04 * q.nguyengia)
                    ELSE COALESCE(q.nguyengia, 0)
                END as current_value
            ";

            $assetTypeMap = [
                'Cống đập' => ['table' => 'taisan.congdap', 'alias' => 'c'],
                'Trạm bơm' => ['table' => 'taisan.trambom', 'alias' => 't'],
                'Kênh mương' => ['table' => 'taisan.kenhmuong', 'alias' => 'k'],
            ];

            $queriesToUnion = [];
            $filterLoaiTaiSan = $validatedData['loai_tai_san'] ?? null;

            foreach ($assetTypeMap as $typeName => $config) {
                // Nếu có yêu cầu loại tài sản cụ thể và không phải là loại hiện tại, bỏ qua
                if (!empty($filterLoaiTaiSan) && $filterLoaiTaiSan !== 'all' && $filterLoaiTaiSan !== $typeName) {
                    continue;
                }

                $tableAlias = $config['alias'];
                $query = DB::connection('pgsql')
                    ->table("{$config['table']} as {$tableAlias}")
                    ->leftJoin('taisan.quyettoan as q', "{$tableAlias}.id_qt", '=', 'q.id')
                    ->leftJoin('basemap.rg_xa as r', "{$tableAlias}.id_xa", '=', 'r.id')
                    ->selectRaw($commonSelect) // $commonSelect sử dụng các cột từ bảng chính (đã được alias) và bảng join
                    ->addSelect(DB::raw("'$typeName' as loai_tai_san"));

                // Áp dụng bộ lọc chung
                if (!empty($validatedData['xa_phuong'])) {
                    $query->where('r.tenxa', 'ilike', '%' . $validatedData['xa_phuong'] . '%');
                }
                if (!empty($validatedData['quan_huyen'])) {
                    $query->where('r.tenhuyen', 'ilike', '%' . $validatedData['quan_huyen'] . '%');
                }

                // Áp dụng bộ lọc tình trạng trực tiếp vào SQL
                if (!empty($validatedData['tinh_trang'])) {
                    $rawStatusColumn = "{$tableAlias}.tinhtrang";
                    if ($validatedData['tinh_trang'] === 'active') {
                        $query->where(function ($subQuery) use ($rawStatusColumn) {
                            $subQuery->where($rawStatusColumn, 'NOT ILIKE', '%hư hỏng%')
                                     ->orWhereNull($rawStatusColumn);
                        });
                    } elseif ($validatedData['tinh_trang'] === 'inactive') {
                        $query->where($rawStatusColumn, 'ILIKE', '%hư hỏng%');
                    }
                }
                $queriesToUnion[] = $query;
            }

            // Thực hiện union và lấy kết quả
            $results = collect([]);
            if (!empty($queriesToUnion)) {
                $finalQuery = array_shift($queriesToUnion); // Lấy query đầu tiên
                foreach ($queriesToUnion as $queryToUnion) { // Union các query còn lại
                    $finalQuery->unionAll($queryToUnion); // Sử dụng unionAll để hiệu suất tốt hơn nếu không cần loại bỏ trùng lặp
                }
                $results = $finalQuery->get();
            } else {
                // Trường hợp không có query nào được xây dựng (ví dụ: filter loại tài sản không hợp lệ)
                $results = collect([]);
            }

            // Calculate summary statistics
            $stats = [
                'totals' => [
                    'total_count' => $results->count(),
                    'total_value' => $results->sum('original_price'),
                    'total_current_value' => $results->sum('current_value'),
                    'total_active' => $results->where('hoat_dong', true)->count(),
                    'total_inactive' => $results->where('hoat_dong', false)->count(),
                ],
                'by_type' => $results->groupBy('loai_tai_san')->map(function ($items) {
                    return [
                        'count' => $items->count(),
                        'total_value' => $items->sum('original_price'),
                        'current_value' => $items->sum('current_value'),
                        'active_count' => $items->where('hoat_dong', true)->count(),
                        'inactive_count' => $items->where('hoat_dong', false)->count(),
                    ];
                }),
                'by_unit' => $results->groupBy('unit')->map(function ($items) {
                    return [
                        'count' => $items->count(),
                        'total_value' => $items->sum('original_price'),
                        'current_value' => $items->sum('current_value'),
                        'by_type' => $items->groupBy('loai_tai_san')->map(function ($typeItems) {
                            return [
                                'count' => $typeItems->count(),
                                'total_value' => $typeItems->sum('original_price'),
                                'current_value' => $typeItems->sum('current_value'),
                            ];
                        }),
                    ];
                })
            ];

            return $this->successResponse([
                'data' => $results->values(),
                'stats' => $stats,
                'metadata' => [
                    'report_date' => Carbon::now()->format('Y-m-d'),
                    'filters' => $validatedData
                ]
            ], 'Lấy dữ liệu thống kê tài sản thành công');

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
}
