<template>
  <div class="flex flex-col h-full">
    <LoadingSpinner v-if="isLoading" :text="loadingText" :color="loadingColor" overlay size="lg" />
    <!-- Report Info Modal -->
    <ReportInfoModal v-model:isOpen="showReportInfoModal" @submit="handleReportInfoSubmit" />

    <div class="flex gap-4 mb-2 items-center flex-shrink-0">
      <div class="flex items-center gap-2 text-sm">
        <label>Từ ngày:</label>
        <input type="date" v-model="startDate" class="p-2 border border-gray-300 rounded" />
        <label>Đến ngày:</label>
        <input type="date" v-model="endDate" class="p-2 border border-gray-300 rounded" />
      </div>
      <select v-model="reportType" class="p-2 border border-gray-300 rounded min-w-[150px] max-w-[450px] text-sm">
        <option value="">Chọn loại báo cáo</option>
        <option v-for="(name, code) in REPORT_TYPES_MAP" :key="code" :value="code">
          {{ name }}
        </option>
      </select>
      <button @click="generateReport"
        class="px-4 py-2 bg-[#0077b6] text-white border border-[#0077b6] rounded cursor-pointer font-medium transition-all duration-200 hover:opacity-90 text-sm">Tạo
        báo cáo thống kê</button>
    </div>

    <div class="flex-1 flex flex-col overflow-hidden">
      <div class="flex flex-1 gap-4 overflow-hidden">
        <div class="flex-1 flex flex-col bg-white rounded shadow overflow-hidden min-w-0">
          <div class="flex justify-between items-center pb-2 border-b border-gray-200">
            <h3 class="m-0 text-base font-semibold">Xem trước báo cáo</h3>
            <div class="flex gap-2 items-center">
              <select v-model="exportFormat" class="px-1.5 py-1 border border-gray-300 rounded text-sm">
                <option value="word">Word</option>
                <option value="pdf">PDF</option>
                <option value="excel">Excel</option>
              </select>
              <button @click="exportReportFile"
                class="px-3 py-1.5 bg-[#0077b6] text-white border border-[#0077b6] rounded cursor-pointer font-medium text-sm transition-all duration-200 hover:not(:disabled):opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="!reportGenerated">
                Tải xuống
              </button>
            </div>
          </div>

          <div v-if="!reportGenerated" class="flex-1 flex items-center justify-center p-8">
            <div class="text-center text-gray-500">
              <div class="mb-4 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="stroke-current">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
              </div>
              <p class="text-dm font-semibold">Vui lòng chọn loại báo cáo và nhấn "Tạo báo cáo thống kê" để xem trước</p>
            </div>
          </div>

          <div v-else class="flex-1 flex flex-col overflow-auto p-4">
            <div class="px-6 py-4 text-sm leading-relaxed text-gray-900">
              <!-- Header với logo -->
              <div class="mb-6">
                <div class="flex justify-between">
                  <!-- Bên trái: Cơ quan ban hành -->
                  <div class="text-center leading-tight">
                    <p class="uppercase">Bộ NN&amp;PTNT/UBND</p>
                    <p class="font-bold uppercase">{{ reportInfo?.name || '…' }}</p>
                    <p>-------</p>
                  </div>
                  <!-- Chừa chỗ trống bên phải -->
                  <div class="w-32"></div>
                </div>

                <!-- Tiêu đề báo cáo -->
                <div class="mt-6 text-center">
                  <h2 class="text-xl font-bold uppercase">
                    Báo cáo kê khai tài sản kết cấu hạ tầng thủy lợi
                  </h2>
                </div>
              </div>
            </div>

            <div class="flex-1">
              <div class="mb-6 text-gray-800">
                <h5 class="font-bold mb-3">A. Thông tin về đối tượng báo cáo</h5>

                <template v-if="reportInfo">
                  <div class="flex mb-1">
                    <p class="w-1/2">
                      <span class="font-semibold">Tên đối tượng:</span> {{ reportInfo.name || '…' }}
                    </p>
                    <p class="w-1/2">
                      <span class="font-semibold">Mã đơn vị:</span> {{ reportInfo.code || '………………' }}
                    </p>
                  </div>

                  <p class="mb-1">
                    <span class="font-semibold">Địa chỉ:</span> {{ reportInfo.address || '…' }}
                  </p>

                  <p class="mb-1">
                    <span class="font-semibold">Loại hình:</span> {{ getUnitTypeName(reportInfo.type) || '…' }}
                  </p>
                </template>
              </div>

              <div class="mb-6 text-gray-800">
                <h5 class="font-bold mb-3">B. Thông tin về người lập báo cáo</h5>

                <div class="flex mb-1">
                  <p class="w-1/3">
                    <span class="font-semibold">Họ và tên:</span> {{ currentUser.name || '…' }}
                  </p>
                  <p class="w-1/3">
                    <span class="font-semibold">Điện thoại liên hệ:</span> {{ reportInfo?.phone || '………………' }}
                  </p>
                  <p class="w-1/3">
                    <span class="font-semibold">Email:</span> {{ currentUser.email || '………………' }}
                  </p>
                </div>
              </div>

              <div class="mb-6 text-gray-800">
                <h5 class="text-base font-semibold mb-3">C. Thông tin về tài sản</h5>
                <div class="overflow-auto border border-gray-300">
                  <table class="w-full border-collapse text-sm">
                    <thead>
                      <tr class="text-center">
                        <th class="border border-gray-300 p-1" rowspan="2">TT</th>
                        <th class="border border-gray-300 p-1" rowspan="2">Danh mục tài sản</th>
                        <th class="border border-gray-300 p-1" rowspan="2">Quy mô công trình</th>
                        <th class="border border-gray-300 p-1" rowspan="2">Đơn vị tính</th>
                        <th class="border border-gray-300 p-1" rowspan="2">Số lượng</th>
                        <th class="border border-gray-300 p-1" rowspan="2">Loại công trình</th>
                        <th class="border border-gray-300 p-1" rowspan="2">Năm đưa vào sử dụng</th>
                        <th class="border border-gray-300 p-1" rowspan="2">Diện tích đất (m²)</th>
                        <th class="border border-gray-300 p-1" colspan="3">Giá trị (đồng)</th>
                        <th class="border border-gray-300 p-1" rowspan="2">GTCL</th>
                        <th class="border border-gray-300 p-1" rowspan="2">Chế độ hao mòn/khấu hao</th>
                        <th class="border border-gray-300 p-1" colspan="2">Tình trạng tài sản</th>
                        <th class="border border-gray-300 p-1" rowspan="2">Ghi chú</th>
                      </tr>
                      <tr class="text-center">
                        <th class="border border-gray-300 p-1">Nguyên giá</th>
                        <th class="border border-gray-300 p-1">Hao mòn (lũy kế)</th>
                        <th class="border border-gray-300 p-1">Khấu hao (lũy kế)</th>
                        <th class="border border-gray-300 p-1">Hoạt động</th>
                        <th class="border border-gray-300 p-1">Không hoạt động</th>
                      </tr>
                      <tr class="text-center">
                        <th class="border border-gray-300 p-1 font-normal">1</th>
                        <th class="border border-gray-300 p-1 font-normal">2</th>
                        <th class="border border-gray-300 p-1 font-normal">3</th>
                        <th class="border border-gray-300 p-1 font-normal">4</th>
                        <th class="border border-gray-300 p-1 font-normal">5</th>
                        <th class="border border-gray-300 p-1 font-normal">6</th>
                        <th class="border border-gray-300 p-1 font-normal">7</th>
                        <th class="border border-gray-300 p-1 font-normal">8</th>
                        <th class="border border-gray-300 p-1 font-normal">9</th>
                        <th class="border border-gray-300 p-1 font-normal">10</th>
                        <th class="border border-gray-300 p-1 font-normal">11</th>
                        <th class="border border-gray-300 p-1 font-normal">12</th>
                        <th class="border border-gray-300 p-1 font-normal">13</th>
                        <th class="border border-gray-300 p-1 font-normal">14</th>
                        <th class="border border-gray-300 p-1 font-normal">15</th>
                        <th class="border border-gray-300 p-1 font-normal">16</th>
                      </tr>
                    </thead>
                    <tbody v-if="reportGenerated && displayableReportRows.length > 0">
                      <template v-for="(row, rowIndex) in displayableReportRows" :key="`display-row-${rowIndex}`">
                        <tr v-if="row.type === 'header'" class="font-semibold">
                          <td class="border border-gray-300 p-2 text-center font-bold">{{ row.headerRoman }}</td>
                          <td class="border border-gray-300 p-2 text-left font-bold">
                            {{ row.headerText }}
                          </td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                          <td class="border border-gray-300 p-2 text-center font-bold"></td>
                        </tr>
                        <tr v-else-if="row.type === 'data' && row.dataItem" class="text-center hover:bg-gray-50">
                          <td class="border border-gray-300 p-1">
                            {{ reportData.indexOf(row.dataItem) + 1 }}
                          </td>
                          <td class="border border-gray-300 p-1 text-left">{{ row.dataItem.name }}</td>
                          <td class="border border-gray-300 p-1 text-left">{{ row.dataItem.quy_mo }}</td>
                          <td class="border border-gray-300 p-1">{{ row.dataItem.unit }}</td>
                          <td class="border border-gray-300 p-1">{{ row.dataItem.so_luong }}</td>
                          <td class="border border-gray-300 p-1 text-left">{{ row.dataItem.loai_cong_trinh }}
                          </td>
                          <td class="border border-gray-300 p-1">{{ row.dataItem.nam_su_dung }}</td>
                          <td class="border border-gray-300 p-1 text-right">
                            {{ row.dataItem.dien_tich_dat > 0 ? row.dataItem.dien_tich_dat.toLocaleString('vi-VN') : ''
                            }}
                          </td>
                          <td v-if="row.dataItem.shouldRenderFinancialCells"
                            class="border border-gray-300 p-1 text-right" :rowspan="row.dataItem.financialRowspan">
                            {{ formatCurrency(row.dataItem.originalPrice) }}
                          </td>
                          <td v-if="row.dataItem.shouldRenderFinancialCells"
                            class="border border-gray-300 p-1 text-right" :rowspan="row.dataItem.financialRowspan">
                            {{ formatCurrency(row.dataItem.hao_mon) }}
                          </td>
                          <td v-if="row.dataItem.shouldRenderFinancialCells"
                            class="border border-gray-300 p-1 text-right" :rowspan="row.dataItem.financialRowspan">
                            {{ formatCurrency(row.dataItem.khau_hao_tinh) }}
                          </td>
                          <td v-if="row.dataItem.shouldRenderFinancialCells"
                            class="border border-gray-300 p-1 text-right" :rowspan="row.dataItem.financialRowspan">
                            {{ formatCurrency(row.dataItem.currentValue) }}
                          </td>
                          <td class="border border-gray-300 p-1">{{ row.dataItem.che_do_hao_mon }}</td>
                          <td class="border border-gray-300 p-1">{{ row.dataItem.hoat_dong ? 'X' : '' }}</td>
                          <td class="border border-gray-300 p-1">{{ !row.dataItem.hoat_dong ? 'X' : '' }}</td>
                          <td class="border border-gray-300 p-1 text-left">{{ row.dataItem.ghi_chu }}</td>
                        </tr>
                        <tr v-else-if="row.type === 'empty_section_row'">
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                          <td class="border border-gray-300 p-3 text-center text-gray-500"></td>
                        </tr>
                      </template>
                    </tbody>
                    <tbody v-else-if="reportGenerated">
                      <tr>
                        <td colspan="16" class="text-center p-4 text-gray-500">
                          Không có dữ liệu tài sản để hiển thị.
                        </td>
                      </tr>
                    </tbody>
                    <!-- Dòng tổng cộng -->
                    <tfoot v-if="reportGenerated && reportData.length > 0">
                      <tr class="font-bold bg-gray-100 text-center">
                        <td class="border border-gray-300 p-1 text-center" :colspan="2">Tổng cộng</td>
                        <td class="border border-gray-300 p-1"></td>
                        <td class="border border-gray-300 p-1"></td>
                        <td class="border border-gray-300 p-1"></td>
                        <td class="border border-gray-300 p-1"></td>
                        <td class="border border-gray-300 p-1"></td>
                        <!-- Cột Diện tích đất -->
                        <td class="border border-gray-300 p-1 text-right">
                           {{ reportSummary.totals.total_area > 0 ? reportSummary.totals.total_area.toLocaleString('vi-VN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '' }}
                        </td>
                        <!-- Cột Nguyên giá -->
                        <td class="border border-gray-300 p-1 text-right">
                          {{ formatCurrency(reportSummary.totals.total_original_value) }}
                        </td>
                         <!-- Cột Hao mòn (lũy kế) - Có thể để trống nếu không có tổng riêng -->
                        <td class="border border-gray-300 p-1 text-right">
                           <!-- {{ formatCurrency(reportSummary.totals.total_depreciation) }} -->
                        </td>
                        <!-- Cột Khấu hao (lũy kế) -->
                        <td class="border border-gray-300 p-1 text-right">
                           {{ formatCurrency(reportSummary.totals.total_depreciation) }}
                        </td>
                        <!-- Cột GTCL -->
                        <td class="border border-gray-300 p-1 text-right">{{ formatCurrency(reportSummary.totals.total_remaining_value) }}</td>
                        <td class="border border-gray-300 p-1"></td>
                        <td class="border border-gray-300 p-1">{{ reportSummary.totals.total_active !== undefined ? reportSummary.totals.total_active : '' }}</td>
                        <td class="border border-gray-300 p-1">{{ reportSummary.totals.total_inactive !== undefined ? reportSummary.totals.total_inactive : '' }}</td>
                        <td class="border border-gray-300 p-1"></td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              <div class="flex justify-between px-6">
                <!-- Cột 1 -->
                <div class="w-1/3"></div>

                <!-- Cột 2 -->
                <div class="w-1/3"></div>

                <!-- Cột 3: Ký tên -->
                <div class="w-1/3 text-center">
                  <p class="italic mb-1">HCM, <span class="italic">{{ currentDateFormatted }}</span></p>
                  <p class="font-semibold uppercase mb-1">Thủ trưởng đơn vị báo cáo</p>
                  <p class="italic text-gray-700">(Ký, ghi rõ họ tên và đóng dấu)</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import LoadingSpinner from '../LoadingSpinner.vue'
import ReportInfoModal from './ReportInfoModal.vue'
import axios from 'axios'
import { usePage } from '@inertiajs/vue3'
import type { User } from '@/types' // Đảm bảo đường dẫn này đúng với project của bạn

// --- Interface Definitions ---
interface ReportDataItem {
  name: string;
  quy_mo: string;
  unit: string;
  loai_tai_san: string; // Thêm trường này để sắp xếp
  so_luong: number;
  loai_cong_trinh: string;
  nam_su_dung: string | number;
  dien_tich_dat: number;
  originalPrice: number;
  hao_mon: number;
  khau_hao_tinh: number;
  currentValue: number;
  che_do_hao_mon: string;
  hoat_dong: boolean;
  ghi_chu?: string;
  id_quyettoan: string | number | null;
  financialRowspan?: number;
  shouldRenderFinancialCells?: boolean;
}

interface ReportInformation {
  name: string;
  code: string;
  address: string;
  type: string;
   phone?: string; 
}

interface CurrentUser {
  name: string;
  email: string;
}

interface ReportSummaryTotals {
  total_count: number;
  total_area: number;
  total_original_value: number;
  total_depreciation: number;
  total_remaining_value: number;
  total_active?: number; // Thêm số lượng hoạt động
  total_inactive?: number;
}

interface ReportSummaryByType {
  [key: string]: {
    count: number;
    total_value: number;
  };
}

interface ReportSummaryData {
  totals: ReportSummaryTotals;
  by_type: ReportSummaryByType;
}

// --- Reactive State ---
const reportType = ref('')
const startDate = ref('')
const endDate = ref('')
const reportGenerated = ref(false)
const exportFormat = ref('word') // Default to Word, matching the first option
const reportData = ref<ReportDataItem[]>([])
const showReportInfoModal = ref(false)
const reportInfo = ref<ReportInformation | null>(null)
const reportMetadata = ref<any>(null) // Lưu trữ metadata của báo cáo
const reportSummary = ref<ReportSummaryData>({
  totals: {
    total_count: 0,
    total_area: 0,
    total_original_value: 0,
    total_depreciation: 0,
    total_remaining_value: 0,
    total_active: 0, // Khởi tạo
    total_inactive: 0 // Khởi tạo
  },
  by_type: {}
})

// --- Loading Spinner State ---
const isLoading = ref(false)
const loadingText = ref('')
const loadingColor = ref<'primary' | 'secondary' | 'white'>('primary');

function showSpinner(text: string, color: 'primary' | 'secondary' | 'white' = 'primary', autoHideDelay?: number) {
  loadingText.value = text;
  loadingColor.value = color;
  isLoading.value = true;
  if (autoHideDelay) {
    setTimeout(() => {
      isLoading.value = false;
    }, autoHideDelay);
  }
}
function hideSpinner() {
  isLoading.value = false;
  loadingText.value = '';
}
const page = usePage()

const currentUser = computed<CurrentUser>(() => {
  // Giả định page.props.auth.user có kiểu User và có thể có thêm thuộc tính 'phone'
  // Hoặc bạn cần lấy 'phone' từ một nguồn khác nếu nó không có sẵn trên auth.user
  const authUser = page.props.auth?.user as User;

  if (authUser) {
    return {
      name: authUser.name || 'N/A',
      email: authUser.email || 'N/A',
    };
  }
  // Fallback nếu không có thông tin người dùng (ví dụ: trang không yêu cầu đăng nhập)
  return { name: 'Không xác định', email: 'N/A' };
});

const currentDateFormatted = computed(() => {
  const today = new Date();
  const day = String(today.getDate()).padStart(2, '0');
  const month = String(today.getMonth() + 1).padStart(2, '0'); // Tháng trong JavaScript bắt đầu từ 0
  const year = today.getFullYear();
  return `ngày ${day} tháng ${month} năm ${year}`;
});

const tableColumnCount = 16; // Số cột trong bảng dữ liệu, dùng cho colspan

// Computed property để tạo danh sách các dòng hiển thị (bao gồm header mục và dữ liệu)
const displayableReportRows = computed(() => {
  if (!reportGenerated.value) return [];

  type ReportRow =
    | { type: 'header'; headerRoman: string; headerText: string }
    | { type: 'data'; dataItem: ReportDataItem }
    | { type: 'empty_section_row' };

  const rows: ReportRow[] = [];

  // groupSortOrder đã được định nghĩa bên dưới trong Constants
  // sectionHeadingsMap cũng đã được định nghĩa

  for (const groupName of groupSortOrder) {
    const fullTitle = sectionHeadingsMap[groupName] || groupName;
    let headerRoman = '';
    let headerText = fullTitle;

    // Tách chuỗi dạng "I. Tên mục" thành "I." và "Tên mục"
    const parts = fullTitle.split('. ', 2);
    if (parts.length > 1) {
      headerRoman = parts[0] + '.'; // Ví dụ: "I."
      headerText = parts[1];    // Ví dụ: "Hồ chứa"
    } else {
      // Nếu không có dạng "X. Yyy", thì toàn bộ là headerText
      headerText = fullTitle;
    }

    // Add header row for the section
    rows.push({
      type: 'header',
      headerRoman,
      headerText
    });

    // Lọc các item thuộc groupName từ reportData (đã được sắp xếp và xử lý rowspan)
    const itemsInGroup = reportData.value.filter(item => (item.loai_tai_san || 'Không xác định') === groupName);

    if (itemsInGroup.length > 0) {
      itemsInGroup.forEach(item => {
        rows.push({ type: 'data', dataItem: item });
      });
    } else {
      // Add empty row for sections with no data
      rows.push({ type: 'empty_section_row' });
    }
  }
  return rows;
});

// --- Constants ---
const REPORT_TYPES_MAP: Record<string, string> = {
  'b1bb': 'Mẫu số 01/BB - Biên bản bàn giao, tiếp nhận TSKCHTTHL',
  'b1dm': 'Mẫu số 01/DM - Danh mục giao TSKCHTTHL',
  'b2dm': 'Mẫu số 02/DM - Danh mục khai thác TSKCHTTHL',
  'b1a': 'Mẫu số 01A - Báo cáo kê khai TSKCHTTHL',
  'b1b': 'Mẫu số 01B - Báo cáo kê khai bổ sung thông tin về TSKCHTTHL',
  'b1c': 'Mẫu số 01C - Báo cáo kê khai tăng TSKCHTTHL',
  'b1d': 'Mẫu số 01D - Báo cáo kê khai giảm TSKCHTTHL',
  'b2a': 'Mẫu số 02A - Báo cáo tình hình khai thác TSKCHTTHL (Phương thức: Trực tiếp tổ chức thực hiện khai thác tài sản)',
  'b2b': 'Mẫu số 02B - Báo cáo tình hình khai thác TSKCHTTHL (Phương thức: Cho thuê quyền khai thác tài sản)',
  'b2c': 'Mẫu số 02C - Báo cáo tình hình khai thác TSKCHTTHL (Phương thức: Chuyển nhượng có thời hạn quyền khai thác tài sản)',
  'b3a': 'Mẫu số 03A - Báo cáo tổng hợp tình hình quản lý, sử dụng và khai thác TSKCHTTHL',
  'b3b': 'Mẫu số 03B - Báo cáo tổng hợp tình hình khai thác TSKCHTTHL',
  'b3c': 'Mẫu số 03C - Báo cáo tổng hợp tình hình xử lý TSKCHTTHL',
  'b3d': 'Mẫu số 03D - Báo cáo tổng hợp tình hình tăng, giảm TSKCHTTHL',
  'b3đ': 'Mẫu số 03Đ - Báo cáo tổng hợp tình hình quản lý, sử dụng số tiền thu từ khai thác TSKCHTTHL',
  'b3e': 'Mẫu số 03E - Báo cáo tổng hợp tình hình quản lý, sử dụng số tiền thu từ xử lý TSKCHTTHL'
};

const UNIT_TYPES_MAP: Record<string, string> = {
  'state': 'Cơ quan nhà nước',
  'public': 'Đơn vị sự nghiệp công lập',
  'enterprise': 'Doanh nghiệp nhà nước'
};

const REPORT_API_ENDPOINTS: Record<string, string> = {
  'b1a': '/api/reports/b1a',
  // Add other report type endpoints here: e.g., 'b1bb': '/api/reports/b1bb',
};

const groupSortOrder = ['Hồ chứa', 'Đập dâng', 'Trạm bơm', 'Cống đập', 'Kênh mương'];

const sectionHeadingsMap: Record<string, string> = {
  'Hồ chứa': 'I. Hồ chứa',
  'Đập dâng': 'II. Đập dâng',
  'Trạm bơm': 'III. Trạm bơm',
  'Cống đập': 'IV. Cống đập',
  'Kênh mương': 'V. Kênh mương',
  // Thêm các loại tài sản khác nếu có và muốn có tiêu đề mục riêng
};

// --- Methods ---
async function generateReport() {
  // Kiểm tra xem đã chọn loại báo cáo chưa
  if (!reportType.value) {
    showSpinner('Vui lòng chọn loại báo cáo.', 'secondary', 3000);
    return
  }

  // Hiển thị modal để nhập thông tin đơn vị
  showReportInfoModal.value = true
}

async function handleReportInfoSubmit(data: ReportInformation) {
  reportInfo.value = data
  // Đặt lại trạng thái trước khi lấy dữ liệu mới
  reportGenerated.value = false
  reportData.value = []

  // Spinner will be shown/hidden within fetchAndSetReportData
  await fetchAndSetReportData(reportType.value, startDate.value, endDate.value)

  if (reportGenerated.value) {
    showSpinner(`Đã tạo báo cáo ${getReportTypeName()}`, 'primary', 3000);
  }
}

async function fetchAndSetReportData(type: string, start: string, end: string) {
  const apiUrl = REPORT_API_ENDPOINTS[type];
  if (!apiUrl) {
    showSpinner(`Chức năng xuất báo cáo cho "${getReportTypeName()}" chưa được cấu hình API.`, 'secondary', 5000);
    reportGenerated.value = false
    reportData.value = []
    return
  }
  showSpinner('Đang tải dữ liệu báo cáo...', 'primary');
  try {
    const response = await axios.get(apiUrl)
    // Assuming response.data.data contains { data: [], summary: {}, metadata: {} }
    const { data: apiData, summary, metadata } = response.data.data

    // Reset report data
    reportData.value = []

    if (Array.isArray(apiData)) {
      reportData.value = apiData.map((item: any): ReportDataItem => {
        const normalizedTinhTrang = (item.tinh_trang || '').toLowerCase().trim();
        return {
          name: item.ten || item.ten_tai_san || '',
          quy_mo: item.quy_mo || item.quy_mo_cong_trinh || 'Chưa có thông tin',
          unit: item.don_vi_tinh || (item.loai_tai_san === 'Trạm bơm' ? 'Trạm' : (item.loai_tai_san === 'Kênh mương' ? 'Km' : 'Cái')),
          loai_tai_san: item.loai_tai_san || 'Không xác định', // Map trường loai_tai_san
          so_luong: parseInt(item.so_luong, 10),
          loai_cong_trinh: item.loai_cong_trinh || 'Không xác định',
          nam_su_dung: item.nam_su_dung || item.nam_dua_vao_su_dung || 'Chưa xác định',
          dien_tich_dat: parseFloat(item.dien_tich_dat) || 0,
          originalPrice: parseFloat(item.nguyen_gia) || 0,
          hao_mon: parseFloat(item.hao_mon_luy_ke || item.hao_mon) || 0,
          khau_hao_tinh: parseFloat(item.khau_hao_luy_ke || item.khau_hao_tinh) || 0,
          currentValue: parseFloat(item.gia_tri_con_lai) || 0,
          che_do_hao_mon: item.che_do_hao_mon_khau_hao || item.che_do_hao_mon || '',
          hoat_dong: normalizedTinhTrang !== 'hư hỏng',
          id_quyettoan: item.id_quyettoan,
          ghi_chu: item.ghi_chu || '',
        };
      });

      // Save metadata if exists
      if (metadata) {
        reportMetadata.value = metadata
      }

      // Sắp xếp reportData:
      // 1. Theo thứ tự tùy chỉnh của loai_tai_san
      // 2. Theo id_quyettoan để nhóm các mục có cùng quyết toán
      // 3. Theo tên tài sản để ổn định thứ tự trong nhóm
      reportData.value.sort((a, b) => {
        const indexA = groupSortOrder.indexOf(a.loai_tai_san); // Sử dụng groupSortOrder đã định nghĩa
        const indexB = groupSortOrder.indexOf(b.loai_tai_san); // Sử dụng groupSortOrder đã định nghĩa

        // Sắp xếp theo loai_tai_san tùy chỉnh
        if (indexA !== -1 && indexB !== -1) { // Cả hai đều nằm trong sortOrder
          if (indexA !== indexB) return indexA - indexB;
        } else if (indexA !== -1) { // Chỉ A nằm trong sortOrder
          return -1;
        } else if (indexB !== -1) { // Chỉ B nằm trong sortOrder
          return 1;
        } else { // Cả hai đều không nằm trong sortOrder, sắp xếp theo alphabet
          const typeCompare = a.loai_tai_san.localeCompare(b.loai_tai_san);
          if (typeCompare !== 0) return typeCompare;
        }

        // Sắp xếp theo id_quyettoan (đẩy null/undefined xuống cuối)
        const idQtA = a.id_quyettoan;
        const idQtB = b.id_quyettoan;
        if (idQtA === null || idQtA === undefined) return (idQtB === null || idQtB === undefined) ? 0 : 1;
        if (idQtB === null || idQtB === undefined) return -1;

        // So sánh id_quyettoan, ưu tiên số nếu cả hai là số, nếu không thì so sánh chuỗi
        const numIdA = Number(idQtA);
        const numIdB = Number(idQtB);
        if (!isNaN(numIdA) && !isNaN(numIdB)) {
            if (numIdA !== numIdB) return numIdA - numIdB;
        } else {
            const strIdCompare = String(idQtA).localeCompare(String(idQtB));
            if (strIdCompare !== 0) return strIdCompare;
        }
        // Sắp xếp theo tên tài sản để đảm bảo thứ tự ổn định
        return (a.name || '').localeCompare(b.name || '');
      });

      // Xử lý rowspan cho các ô tài chính dựa trên id_quyettoan
      if (reportData.value.length > 0) {
        for (let i = 0; i < reportData.value.length;) {
          const currentItem = reportData.value[i];
          currentItem.financialRowspan = 1;
          currentItem.shouldRenderFinancialCells = true;

          let groupSize = 1;
          if (currentItem.id_quyettoan !== null && currentItem.id_quyettoan !== undefined && String(currentItem.id_quyettoan).trim() !== '') {
            for (let j = i + 1; j < reportData.value.length; j++) {
              if (reportData.value[j].id_quyettoan === currentItem.id_quyettoan) {
                groupSize++;
              } else {
                break;
              }
            }
          }
          if (groupSize > 1) {
            currentItem.financialRowspan = groupSize;
            for (let k = 1; k < groupSize; k++) {
              reportData.value[i + k].shouldRenderFinancialCells = false;
            }
          }
          i += groupSize;
        }
      }

      // API được cho là luôn cung cấp 'summary' đầy đủ.
      if (summary && summary.totals) { // Thêm kiểm tra summary.totals để an toàn hơn
        reportSummary.value = summary;
      } else {
        console.error('API did not provide valid summary data. Report totals might be incorrect.');
        // Fallback: Khởi tạo reportSummary với giá trị mặc định để tránh lỗi template
        reportSummary.value = {
          totals: { total_count: 0, total_area: 0, total_original_value: 0, total_depreciation: 0, total_remaining_value: 0, total_active: 0, total_inactive: 0 },
          by_type: {}
        };
      }

      reportGenerated.value = true
      hideSpinner();
    } else {
      console.error('Invalid data format from API:', apiData)
      showSpinner('Lỗi: Định dạng dữ liệu báo cáo không hợp lệ.', 'secondary', 5000);
      reportData.value = []
      reportGenerated.value = false
    }
  } catch (error) {
    console.error(`Error loading report data for type ${type}:`, error)
    showSpinner(`Lỗi tải dữ liệu cho báo cáo ${getReportTypeName()}. Vui lòng thử lại.`, 'secondary', 5000);
    reportData.value = []
    reportGenerated.value = false
  }
}

function getReportTypeName() {
  return reportType.value ? REPORT_TYPES_MAP[reportType.value] : 'Chưa chọn loại báo cáo'
}

function formatDate(dateString: string) {
  if (!dateString) return 'Chưa chọn'
  const date = new Date(dateString)
  return date.toLocaleDateString('vi-VN')
}

function getUnitTypeName(type: string) {
  return UNIT_TYPES_MAP[type] || 'Không xác định'
}

function formatCurrency(value: number) {
  return new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(value)
}

// Hàm xuất báo cáo thành file (đã cập nhật để gọi backend)
async function exportReportFile() {
  if (!reportType.value || !reportGenerated.value) {
    showSpinner('Vui lòng tạo báo cáo trước khi tải xuống.', 'secondary', 3000);
    return
  }

  // TODO: Xử lý các định dạng xuất khác nhau (Excel, Word)
  if (exportFormat.value !== 'word' || reportType.value !== 'b1a') {
    showSpinner(`Chức năng tải xuống định dạng ${exportFormat.value.toUpperCase()} cho báo cáo ${getReportTypeName()} chưa được triển khai đầy đủ.`, 'secondary', 5000);
    return;
  }

  if (!reportInfo.value) {
    showSpinner('Vui lòng cung cấp thông tin đơn vị (tạo lại báo cáo nếu cần) trước khi tải xuống.', 'secondary', 4000);
    return;
  }

  // URL của route backend để xuất file Word Form 01A
  const exportUrl = `/api/reports/b1a/export`;

  showSpinner(`Đang chuẩn bị tải xuống báo cáo ${getReportTypeName()} định dạng ${exportFormat.value.toUpperCase()}...`, 'primary');

  try {
    const response = await axios.get(exportUrl, {
      params: {
        startDate: startDate.value, // Gửi ngày bắt đầu
        endDate: endDate.value,     // Gửi ngày kết thúc
        don_vi_bao_cao: reportInfo.value?.name,
        ma_don_vi_bao_cao: reportInfo.value?.code,
        dia_chi_don_vi: reportInfo.value?.address,
        loai_hinh_don_vi: reportInfo.value?.type, // Mã loại hình đơn vị
        nguoi_lap_ten: currentUser.value.name,
        nguoi_lap_sdt: reportInfo.value?.phone,
        nguoi_lap_email: currentUser.value.email,
      },
      responseType: 'blob', // Yêu cầu dữ liệu trả về dưới dạng Blob
    });

    const extensionMap: Record<string, string> = {
      word: 'docx',
      pdf: 'pdf',
      excel: 'xlsx',
    };
    const fileExtension = extensionMap[exportFormat.value] || 'dat';
    let filename = `BaoCao_${reportType.value || 'TongHop'}_${new Date().toISOString().split('T')[0]}.${fileExtension}`;

    // Ưu tiên tên file từ header Content-Disposition nếu có
    const contentDisposition = response.headers['content-disposition'];
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?(.+)"?/i);
      if (filenameMatch && filenameMatch.length > 1) {
        filename = filenameMatch[1];
      }
    }

    // Tạo link tải file
    const blob = new Blob([response.data], { type: response.headers['content-type'] });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href); // Giải phóng bộ nhớ

    showSpinner(`Đã tải xuống thành công: ${filename}`, 'primary', 3000);

  } catch (error: any) { // Gõ error là any để truy cập các thuộc tính
    console.error('Error exporting report:', error);
    hideSpinner(); // Hide "Đang chuẩn bị tải xuống..." spinner
    if (error.response && error.response.data instanceof Blob && error.response.data.type.includes('application/json')) {
      // Nếu server trả về lỗi JSON dưới dạng Blob
      const errorText = await error.response.data.text();
      try {
        const errorJson = JSON.parse(errorText);
        showSpinner(`Lỗi khi tải báo cáo: ${errorJson.message || 'Lỗi không xác định từ server.'}`, 'secondary', 5000); // errorJson.message đã an toàn
      } catch (e) {
        showSpinner(`Lỗi khi tải báo cáo: ${errorText}`, 'secondary', 5000); // errorText đã an toàn
      }
    } else {
      showSpinner('Đã xảy ra lỗi khi tải xuống báo cáo. Vui lòng kiểm tra console.', 'secondary', 5000);
    }
  }
}
</script>