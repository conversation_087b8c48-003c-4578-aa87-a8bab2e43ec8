<?php

namespace App\Services;

use App\Repositories\Contracts\CongdapRepositoryInterface;
use App\Exceptions\Taisan\CongdapNotFoundException;
use App\Exceptions\Taisan\InvalidGeometryException;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Models\API\Taisan\Congdap;

class CongdapService
{
    protected $repository;

    public function __construct(CongdapRepositoryInterface $repository)
    {
        $this->repository = $repository;
    }

    public function getList(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->paginate($filters, $perPage);
    }

    /**
     * L<PERSON>y danh sách chỉ bao gồm geometry (không có thuộc tính)
     * Tối ưu tài nguyên khi chỉ cần hiển thị dữ liệu không gian
     */
    public function getGeometryList(array $filters = []): LengthAwarePaginator
    {
        return $this->repository->paginateGeometryOnly($filters);
    }

    /**
     * <PERSON><PERSON><PERSON> danh sách chỉ bao gồm thuộc tính (không có geometry)
     * Tối ưu tài nguyên khi chỉ cần hiển thị thông tin thuộc tính
     */
    public function getAttributesList(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return $this->repository->paginateAttributesOnly($filters, $perPage);
    }

    public function getById(string $id): Congdap
    {
        $congdap = $this->repository->findById($id);

        if (!$congdap) {
            throw new CongdapNotFoundException($id);
        }

        return $congdap;
    }

    public function create(array $data): Congdap
    {
        $this->validateGeometry($data);
        return $this->repository->create($data);
    }

    public function update(string $id, array $data): Congdap
    {
        if (isset($data['geometry'])) {
            $this->validateGeometry($data);
        }

        $updated = $this->repository->update($id, $data);

        if (!$updated) {
            throw new CongdapNotFoundException($id);
        }

        return $updated;
    }

    public function delete(string $id): bool
    {
        if (!$this->repository->checkExists($id)) {
            throw new CongdapNotFoundException($id);
        }

        return $this->repository->delete($id);
    }

    protected function validateGeometry(array $data): void
    {
        if (!isset($data['geometry']) ||
            !isset($data['geometry']['type']) ||
            !isset($data['geometry']['coordinates'])) {
            throw new InvalidGeometryException();
        }
    }
}