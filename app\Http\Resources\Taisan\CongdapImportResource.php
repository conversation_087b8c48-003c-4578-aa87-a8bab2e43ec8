<?php

namespace App\Http\Resources\Taisan;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CongdapImportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * This resource is used for import statistics and results
     */
    public function toArray(Request $request): array
    {
        // Chỉ coi là success nếu có ít nhất một bản ghi được import thành công.
        $isSuccess = $this->resource['successful_imports'] > 0;

        return [
            'success' => $isSuccess,
            'message' => $this->getImportMessage(),
            'statistics' => [
                'total_rows' => $this->resource['total_rows'],
                'processed_rows' => $this->resource['processed_rows'],
                'successful_imports' => $this->resource['successful_imports'],
                'failed_imports' => $this->resource['failed_imports'],
                'success_rate' => $this->calculateSuccessRate(),
            ],
            'performance' => [
                'processing_time_ms' => $this->resource['processing_time'],
                'memory_usage_mb' => $this->resource['memory_usage'],
                'average_time_per_row' => $this->calculateAverageTimePerRow(),
            ],
            'errors' => CongdapImportErrorResource::collection($this->resource['errors'] ?? []),
            'summary' => $this->generateSummary(),
        ];
    }

    /**
     * Generate appropriate import message
     */
    protected function getImportMessage(): string
    {
        $successful = $this->resource['successful_imports'];
        $failed = $this->resource['failed_imports'];
        $total = $this->resource['total_rows'];
        $processed = $this->resource['processed_rows'];

        if ($total === 0) {
            return 'File không có dữ liệu hoặc rỗng.';
        }

        if ($failed === 0 && $successful > 0) {
            return "Import thành công {$successful}/{$total} bản ghi.";
        } elseif ($processed === 0 && $total > 0) {
            return "Import thất bại. Không có bản ghi nào được xử lý. Vui lòng kiểm tra chi tiết lỗi.";
        } elseif ($successful === 0) {
            return "Import thất bại. Không có bản ghi nào được nhập thành công. Lỗi: {$failed}/{$total}.";
        } else {
            return "Import hoàn thành với một số lỗi. Thành công: {$successful}/{$total}, Lỗi: {$failed}/{$total}.";
        }
    }

    /**
     * Calculate success rate percentage
     */
    protected function calculateSuccessRate(): float
    {
        $total = $this->resource['processed_rows'];
        if ($total === 0) {
            return 0.0;
        }

        return round(($this->resource['successful_imports'] / $total) * 100, 2);
    }

    /**
     * Calculate average processing time per row
     */
    protected function calculateAverageTimePerRow(): float
    {
        $processed = $this->resource['processed_rows'];
        if ($processed === 0) {
            return 0.0;
        }

        return round($this->resource['processing_time'] / $processed, 2);
    }

    /**
     * Generate import summary
     */
    protected function generateSummary(): array
    {
        $successful = $this->resource['successful_imports'];
        $failed = $this->resource['failed_imports'];
        $total = $this->resource['total_rows'];

        $summary = [
            'status' => $failed === 0 ? 'success' : ($successful === 0 ? 'failed' : 'partial'),
            'completion_percentage' => $total > 0 ? round(($this->resource['processed_rows'] / $total) * 100, 2) : 0,
        ];

        // Add recommendations based on results
        if ($failed > 0) {
            $summary['recommendations'] = $this->getRecommendations();
        }

        return $summary;
    }

    /**
     * Get recommendations based on import results
     */
    protected function getRecommendations(): array
    {
        $recommendations = [];
        $errors = $this->resource['errors'] ?? [];

        // Analyze common error patterns
        $errorTypes = [];
        foreach ($errors as $error) {
            $errorMessage = $error['error'] ?? '';
            
            if (strpos($errorMessage, 'bắt buộc') !== false) {
                $errorTypes['required_fields'] = ($errorTypes['required_fields'] ?? 0) + 1;
            } elseif (strpos($errorMessage, 'vượt quá') !== false) {
                $errorTypes['length_exceeded'] = ($errorTypes['length_exceeded'] ?? 0) + 1;
            } elseif (strpos($errorMessage, 'năm') !== false) {
                $errorTypes['invalid_year'] = ($errorTypes['invalid_year'] ?? 0) + 1;
            } elseif (strpos($errorMessage, 'tọa độ') !== false || strpos($errorMessage, 'độ') !== false) {
                $errorTypes['coordinate_errors'] = ($errorTypes['coordinate_errors'] ?? 0) + 1;
            }
        }

        // Generate recommendations based on error patterns
        if (isset($errorTypes['required_fields']) && $errorTypes['required_fields'] > 0) {
            $recommendations[] = 'Kiểm tra và điền đầy đủ các trường bắt buộc (đặc biệt là tên công trình).';
        }

        if (isset($errorTypes['length_exceeded']) && $errorTypes['length_exceeded'] > 0) {
            $recommendations[] = 'Rút gọn nội dung các trường dữ liệu để không vượt quá giới hạn ký tự cho phép.';
        }

        if (isset($errorTypes['invalid_year']) && $errorTypes['invalid_year'] > 0) {
            $recommendations[] = 'Kiểm tra định dạng năm (phải là số nguyên từ 1900 đến ' . (date('Y') + 10) . ').';
        }

        if (isset($errorTypes['coordinate_errors']) && $errorTypes['coordinate_errors'] > 0) {
            $recommendations[] = 'Kiểm tra tọa độ (vĩ độ: -90 đến 90, kinh độ: -180 đến 180).';
        }

        if (empty($recommendations)) {
            $recommendations[] = 'Kiểm tra lại dữ liệu và định dạng file Excel theo mẫu chuẩn.';
        }

        return $recommendations;
    }
}
