<?php

namespace App\Http\Resources\Taisan;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CongdapGeometryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Xử lý geometry từ chuỗi GeoJSON
        $geometry = null;
        if ($this->geom) {
            $geometry = json_decode($this->geom);
        }

        return [
            'type' => 'Feature',
            'id' => $this->id,
            'geometry' => $geometry,
            'properties' => [
                'id' => $this->id
            ]
        ];
    }
}
