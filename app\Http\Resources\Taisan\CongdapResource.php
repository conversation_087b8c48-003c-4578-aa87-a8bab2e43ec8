<?php

namespace App\Http\Resources\Taisan;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CongdapResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        // Xử lý geometry trước
        $geometry = null;
        if (isset($this->geom) && is_string($this->geom)) {
             $decodedGeom = json_decode($this->geom);
             $geometry = (json_last_error() === JSON_ERROR_NONE) ? $decodedGeom : null;
        }

        return [
            'type' => 'Feature',
            'id' => $this->id, // id từ Congdap
            'geometry' => $geometry, // Geometry đã xử lý
            'properties' => [
                'id_qt' => $this->id_qt,
                'id_xa' => $this->id_xa,
                // <<< THAY ĐỔI: Truy cập qua relationship đã load >>>
                'tenxa' => $this->whenLoaded('xa', $this->xa->tenxa ?? null),
                'nguyengia' => $this->whenLoaded('quyettoan', $this->quyettoan->nguyengia ?? null),
                // --- Các trường còn lại của Congdap ---
                'ten' => $this->ten,
                'quymo_ct' => $this->quymo_ct,
                'loai_ct' => $this->loai_ct,
                'nam_xd' => $this->nam_xd,
                'nam_sd' => $this->nam_sd,
                'dt_dat' =>(float) $this->dt_dat,
                'tinhtrang' => $this->tinhtrang,
                'quytrinh_vh' => $this->quytrinh_vh,
                'quytrinh_bt' => $this->quytrinh_bt,
                'dv_quanly' => $this->dv_quanly,
                'phuongthuc' => $this->phuongthuc,
                'chuthich' => $this->chuthich,
            ]
        ];
    }
}
