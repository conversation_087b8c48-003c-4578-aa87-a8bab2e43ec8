<template>
  <Transition enter-active-class="transition ease-out duration-200" enter-from-class="opacity-0"
    enter-to-class="opacity-100" leave-active-class="transition ease-in duration-150" leave-from-class="opacity-100"
    leave-to-class="opacity-0">
    <div v-if="modelValue" class="fixed inset-0 z-50 flex items-center justify-center bg-black/75" aria-modal="true"
      role="dialog">
      <Transition enter-active-class="transition ease-out duration-300"
        enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        enter-to-class="opacity-100 translate-y-0 sm:scale-100" leave-active-class="transition ease-in duration-200"
        leave-from-class="opacity-100 translate-y-0 sm:scale-100"
        leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        <div v-if="modelValue" class="relative w-full max-w-2xl mx-4 bg-white rounded-lg shadow-xl">
          <div class="flex items-center justify-between px-6 py-2 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">Thêm Người Dùng Mới</h3>
          </div>
          <div class="px-6 py-4 overflow-y-auto" style="max-height: 75vh;">
            <!-- Trạng thái Loading -->
            <div v-if="isLoading" class="flex items-center justify-center py-10">
              <svg class="w-8 h-8 mr-3 -ml-1 text-blue-500 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
              <span class="text-gray-600">Đang tải dữ liệu...</span>
            </div>

            <!-- Trạng thái Thành công -->
            <div v-else-if="isSuccess" class="flex flex-col items-center justify-center py-10 text-center">
              <svg class="w-16 h-16 text-green-500 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h4 class="text-lg font-semibold text-gray-800">
                Thêm người dùng thành công!
              </h4>
              <p class="text-gray-600">
                Cửa sổ sẽ tự động đóng sau giây lát.
              </p>
            </div>

            <!-- Trạng thái Lỗi -->
            <div v-else-if="error" class="px-4 py-3 text-red-700 bg-red-100 border border-red-400 rounded" role="alert">
              <strong class="font-bold">Lỗi!</strong>
              <span class="block sm:inline"> {{ error }}</span>
            </div>

            <!-- Form nhập liệu -->
            <div v-else class="space-y-4">
              <div class="grid grid-cols-1 gap-4">
                <!-- Tên người dùng -->
                <div class="space-y-1">
                  <label for="name" class="block text-sm font-medium text-gray-700">Tên người dùng *</label>
                  <input type="text" id="name" v-model="formData.name"
                    class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Nhập tên người dùng" />
                </div>

                <!-- Email -->
                <div class="space-y-1">
                  <label for="email" class="block text-sm font-medium text-gray-700">Email *</label>
                  <input type="email" id="email" v-model="formData.email"
                    class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Nhập địa chỉ email" />
                </div>

                <!-- Mật khẩu -->
                <div class="space-y-1">
                  <label for="password" class="block text-sm font-medium text-gray-700">Mật khẩu *</label>
                  <input type="password" id="password" v-model="formData.password"
                    class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Nhập mật khẩu" />
                </div>

                <!-- Xác nhận mật khẩu -->
                <div class="space-y-1">
                  <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Xác nhận mật khẩu *</label>
                  <input type="password" id="password_confirmation" v-model="formData.password_confirmation"
                    class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Nhập lại mật khẩu" />
                </div>

                <!-- Vai trò chính -->
                <div class="space-y-1">
                  <label for="roles" class="block text-sm font-medium text-gray-700">Vai trò chính</label>
                  <select id="roles" v-model="formData.selectedRole" :disabled="isLoadingRoles"
                    class="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">Chọn vai trò chính</option>
                    <option v-for="role in roleOptions" :key="role.name" :value="role.name">
                      {{ role.vi_name }} {{ role.description ? `(${role.description})` : '' }}
                    </option>
                  </select>
                  <div v-if="roleError" class="mt-1 text-sm text-red-600">
                    {{ roleError }}
                  </div>
                </div>

                <!-- Quyền bổ sung -->
                <div class="space-y-1">
                  <label class="block text-sm font-medium text-gray-700">Quyền bổ sung (tùy chọn)</label>
                  <div class="max-h-48 overflow-y-auto border border-gray-300 rounded-md p-3 space-y-2">
                    <div v-for="group in permissionGroups" :key="group.name" class="space-y-1">
                      <h4 class="text-sm font-medium text-gray-800 border-b border-gray-200 pb-1">{{ group.label }}</h4>
                      <div class="grid grid-cols-1 gap-1">
                        <label v-for="permission in group.permissions" :key="permission.name"
                          class="flex items-center space-x-2 text-sm">
                          <input type="checkbox"
                            :value="permission.name"
                            v-model="formData.selectedPermissions"
                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                          <span>{{ permission.vi_name }}</span>
                        </label>
                      </div>
                    </div>
                  </div>
                  <p class="text-xs text-gray-500">Chọn thêm quyền ngoài vai trò chính để tạo quyền linh hoạt cho người dùng</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="flex items-center justify-end px-6 py-2 space-x-3 bg-gray-50 rounded-b-lg">
            <button @click="closeModal" type="button" :disabled="isSubmitting || isSuccess"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
              Đóng
            </button>
            <button @click="resetForm" type="button" :disabled="isSubmitting || isSuccess"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
              Đặt lại
            </button>
            <button @click="submitForm" type="button" :disabled="isSubmitting || isSuccess"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
              <span v-if="isSubmitting" class="flex items-center"><svg class="w-4 h-4 mr-2 animate-spin"
                  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg> Đang thêm...</span>
              <span v-else>Thêm mới</span>
            </button>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue';
import type { PropType } from 'vue';
import axios from 'axios';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
});

// Define Emits để thông báo cho parent component
const emit = defineEmits(['update:modelValue', 'user-added'])

// State
const isLoading = ref(false);
const error = ref<string | null>(null);
const isSubmitting = ref(false);
const isSuccess = ref(false); // Thêm trạng thái thành công

// Form data
const formData = ref<Record<string, any>>({
  name: '',
  email: '',
  password: '',
  password_confirmation: '',
  selectedRole: '',
  selectedPermissions: []
})

// Role options
interface RoleOption {
  name: string;
  vi_name: string;
  description?: string;
}
const roleOptions = ref<RoleOption[]>([]);
const isLoadingRoles = ref(false);
const roleError = ref<string | null>(null);

// Permission options
interface PermissionOption {
  name: string;
  vi_name: string;
  group: string;
}
const permissionOptions = ref<PermissionOption[]>([]);
const permissionGroups = ref<any[]>([]);
const isLoadingPermissions = ref(false);
const permissionError = ref<string | null>(null);

// Constants for API timeout and error messages
const API_TIMEOUT = 30000; // 30 seconds
const ERROR_MESSAGES = {
  TIMEOUT: 'Thao tác đã hết thời gian chờ. Vui lòng thử lại.',
  NETWORK: 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng.',
  UNKNOWN: 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
};

// Modal management functions
const resetModalState = () => {
  isLoading.value = false;
  isSubmitting.value = false;
  isSuccess.value = false; // Reset trạng thái thành công
  error.value = null;
};

const closeModal = () => {
  resetModalState();
  emit('update:modelValue', false);
};

// Reset form
const resetForm = () => {
  Object.keys(formData.value).forEach(key => {
    if (key === 'selectedPermissions') {
      formData.value[key] = [];
    } else {
      formData.value[key] = '';
    }
  });
};

const loadRoleOptions = async () => {
  try {
    isLoadingRoles.value = true;
    roleError.value = null;

    // Load roles from API
    const response = await axios.get('/api/roles');
    roleOptions.value = response.data.data || response.data || [];
  } catch (error: any) {
    console.error('Error loading role options:', error);
    roleError.value = error.message || 'Có lỗi xảy ra khi tải danh sách vai trò.';

    // Fallback to hardcoded roles if API fails
    roleOptions.value = [
      { name: 'super_admin', vi_name: 'Siêu quản trị', description: 'Toàn quyền hệ thống' },
      { name: 'admin', vi_name: 'Quản trị viên', description: 'Quản lý người dùng và dữ liệu' },
      { name: 'data_specialist', vi_name: 'Chuyên viên Dữ liệu', description: 'Chuyên về quản lý dữ liệu' },
      { name: 'report_specialist', vi_name: 'Chuyên viên Báo cáo', description: 'Chuyên về tạo và quản lý báo cáo' },
      { name: 'user_manager', vi_name: 'Quản lý Người dùng', description: 'Quản lý tài khoản người dùng' },
      { name: 'viewer', vi_name: 'Người xem', description: 'Chỉ xem dữ liệu và báo cáo' }
    ];
  } finally {
    isLoadingRoles.value = false;
  }
};

const loadPermissionOptions = async () => {
  try {
    isLoadingPermissions.value = true;
    permissionError.value = null;

    // Load permissions from API
    const response = await axios.get('/api/permissions');
    permissionOptions.value = response.data.data || response.data || [];

    // Group permissions by group
    const groups = permissionOptions.value.reduce((acc: any, permission: PermissionOption) => {
      const groupName = permission.group || 'general';
      if (!acc[groupName]) {
        acc[groupName] = [];
      }
      acc[groupName].push(permission);
      return acc;
    }, {});

    // Convert to array with labels
    const groupLabels: Record<string, string> = {
      data: 'Quản lý Dữ liệu',
      report: 'Báo cáo',
      user: 'Quản lý Người dùng',
      system: 'Hệ thống',
      calculation: 'Tính toán',
      profile: 'Hồ sơ',
      general: 'Chung'
    };

    permissionGroups.value = Object.keys(groups).map(groupName => ({
      name: groupName,
      label: groupLabels[groupName] || groupName,
      permissions: groups[groupName]
    }));

  } catch (error: any) {
    console.error('Error loading permission options:', error);
    permissionError.value = error.message || 'Có lỗi xảy ra khi tải danh sách quyền.';

    // Fallback to hardcoded permissions if API fails
    permissionGroups.value = [
      {
        name: 'data',
        label: 'Quản lý Dữ liệu',
        permissions: [
          { name: 'view_data', vi_name: 'Xem dữ liệu', group: 'data' },
          { name: 'create_data', vi_name: 'Tạo dữ liệu', group: 'data' },
          { name: 'edit_data', vi_name: 'Sửa dữ liệu', group: 'data' },
          { name: 'delete_data', vi_name: 'Xoá dữ liệu', group: 'data' },
          { name: 'import_data', vi_name: 'Import dữ liệu', group: 'data' },
          { name: 'export_data', vi_name: 'Export dữ liệu', group: 'data' }
        ]
      },
      {
        name: 'report',
        label: 'Báo cáo',
        permissions: [
          { name: 'create_reports', vi_name: 'Tạo báo cáo', group: 'report' },
          { name: 'export_reports', vi_name: 'Xuất báo cáo', group: 'report' }
        ]
      },
      {
        name: 'user',
        label: 'Quản lý Người dùng',
        permissions: [
          { name: 'view_users', vi_name: 'Xem người dùng', group: 'user' },
          { name: 'create_users', vi_name: 'Tạo người dùng', group: 'user' },
          { name: 'edit_users', vi_name: 'Sửa người dùng', group: 'user' },
          { name: 'delete_users', vi_name: 'Xoá người dùng', group: 'user' },
          { name: 'assign_roles', vi_name: 'Phân quyền', group: 'user' }
        ]
      }
    ];
  } finally {
    isLoadingPermissions.value = false;
  }
};

// Submit form
const submitForm = async () => {
  try {
    isSubmitting.value = true;
    error.value = null;

    // Validate required fields
    if (!formData.value.name) {
      error.value = 'Vui lòng nhập tên người dùng';
      return;
    }

    if (!formData.value.email) {
      error.value = 'Vui lòng nhập email';
      return;
    }

    if (!formData.value.password) {
      error.value = 'Vui lòng nhập mật khẩu';
      return;
    }

    if (formData.value.password !== formData.value.password_confirmation) {
      error.value = 'Mật khẩu xác nhận không khớp';
      return;
    }

    // Prepare data for API
    const apiData = {
      name: formData.value.name,
      email: formData.value.email,
      password: formData.value.password,
      password_confirmation: formData.value.password_confirmation,
      roles: formData.value.selectedRole ? [formData.value.selectedRole] : [],
      permissions: formData.value.selectedPermissions || []
    };

    console.log('Sending API data:', apiData);

    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('TIMEOUT')), API_TIMEOUT);
    });

    // Create the API request promise
    const apiPromise = axios.post('/api/users', apiData);

    // Race between timeout and API request
    const response = await Promise.race([apiPromise, timeoutPromise]) as any;

    // Validate response success
    if (!response?.data?.success && !response?.status?.toString().startsWith('2')) {
      throw new Error(response?.data?.message || 'Lỗi không xác định từ server');
    }

    // --- SUCCESS HANDLING ---
    isSubmitting.value = false;
    isSuccess.value = true; // Đặt trạng thái thành công

    // Reset form và emit event để làm mới danh sách người dùng
    resetForm();
    emit('user-added');

    // Đợi 1.5 giây để hiển thị thông báo thành công, sau đó đóng modal
    setTimeout(() => {
      closeModal();
    }, 1500);

  } catch (err: any) {
    console.error('Lỗi khi thêm người dùng:', err);

    // Handle different types of errors
    if (err.message === 'TIMEOUT') {
      error.value = ERROR_MESSAGES.TIMEOUT;
    } else if (err.isAxiosError && !err.response) {
      // Network error
      error.value = ERROR_MESSAGES.NETWORK;
    } else {
      // Server error or other errors
      error.value = err.response?.data?.message
        || err.message
        || ERROR_MESSAGES.UNKNOWN;
    }

    // Keep the modal open for error state
    isSubmitting.value = false;
  }
};

// Watch for modal open/close
watch(() => props.modelValue, async (newValue) => {
  if (newValue) {
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
    await Promise.all([
      loadRoleOptions(), // Load roles when modal opens
      loadPermissionOptions() // Load permissions when modal opens
    ]);
  } else {
    document.body.style.overflow = ''; // Restore background scrolling
    resetModalState(); // Reset other modal states if necessary
  }
});

// Handle Escape key for closing modal
const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Escape' && props.modelValue) {
    closeModal();
  }
};

onMounted(() => {
  window.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown);
  // Ensure body scroll is restored if component is unmounted while modal is open
  document.body.style.overflow = '';
});
</script>
