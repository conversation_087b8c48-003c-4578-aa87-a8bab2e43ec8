<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Spatie\Permission\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Throwable;

class RoleController extends Controller
{
    use ApiResponseTrait;

    /**
     * Display a listing of the roles.
     */
    public function index()
    {
        try {
            $roles = Role::with('permissions')->get()->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'vi_name' => $role->vi_name ?? $role->name,
                    'description' => $role->description ?? null,
                    'is_custom' => $role->is_custom ?? false,
                    'permissions_count' => $role->permissions->count(),
                    'created_at' => $role->created_at?->toISOString(),
                    'updated_at' => $role->updated_at?->toISOString(),
                ];
            });

            return $this->successResponse(
                data: $roles,
                message: 'L<PERSON>y danh sách vai trò thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching roles list: ' . $e->getMessage());
            return $this->errorResponse('Lỗi khi lấy danh sách vai trò', 500);
        }
    }

    /**
     * Display the specified role.
     */
    public function show(string $id)
    {
        try {
            $role = Role::with('permissions')->findOrFail($id);

            $roleData = [
                'id' => $role->id,
                'name' => $role->name,
                'vi_name' => $role->vi_name ?? $role->name,
                'description' => $role->description ?? null,
                'is_custom' => $role->is_custom ?? false,
                'permissions' => $role->permissions->map(function ($permission) {
                    return [
                        'id' => $permission->id,
                        'name' => $permission->name,
                        'vi_name' => $permission->vi_name ?? $permission->name,
                        'group' => $permission->group ?? 'general',
                    ];
                }),
                'created_at' => $role->created_at?->toISOString(),
                'updated_at' => $role->updated_at?->toISOString(),
            ];

            return $this->successResponse(
                data: $roleData,
                message: 'Lấy thông tin vai trò thành công'
            );

        } catch (Throwable $e) {
            Log::error('Error fetching role details: ' . $e->getMessage());
            return $this->errorResponse('Không tìm thấy vai trò', 404);
        }
    }
}
