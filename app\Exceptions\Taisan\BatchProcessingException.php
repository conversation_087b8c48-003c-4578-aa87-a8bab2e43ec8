<?php

namespace App\Exceptions\Taisan;

use Exception;

class BatchProcessingException extends Exception
{
    protected $batchNumber;
    protected $processedCount;
    protected $failedCount;
    protected $errors;

    public function __construct(
        int $batchNumber,
        int $processedCount,
        int $failedCount,
        array $errors = [],
        string $message = 'Lỗi xử lý batch dữ liệu'
    ) {
        $this->batchNumber = $batchNumber;
        $this->processedCount = $processedCount;
        $this->failedCount = $failedCount;
        $this->errors = $errors;
        
        $message = "Batch {$batchNumber}: {$message}. Đã xử lý: {$processedCount}, Lỗi: {$failedCount}";
        
        parent::__construct($message);
    }

    public function getBatchNumber(): int
    {
        return $this->batchNumber;
    }

    public function getProcessedCount(): int
    {
        return $this->processedCount;
    }

    public function getFailedCount(): int
    {
        return $this->failedCount;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function render()
    {
        return response()->json([
            'success' => false,
            'message' => $this->getMessage(),
            'code' => 'BATCH_PROCESSING_ERROR',
            'data' => [
                'batch_number' => $this->batchNumber,
                'processed_count' => $this->processedCount,
                'failed_count' => $this->failedCount,
                'errors' => $this->errors
            ]
        ], 500);
    }
}
