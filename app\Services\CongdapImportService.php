<?php

namespace App\Services;

use App\Models\API\Taisan\Congdap;
use App\Repositories\Contracts\CongdapRepositoryInterface;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Exception;

class CongdapImportService
{
    protected $repository;
    protected $batchSize;
    protected $skipErrors;
    
    // Column mapping from Excel headers to database fields
    protected $columnMapping = [
        'id_qt' => ['mã quyết toán'],
        'id_xa' => ['mã xã'],
        'ten' => ['tên công trình'],
        'quymo_ct' => ['quy mô công trình'],
        'loai_ct' => ['loại công trình'],
        'nam_xd' => ['năm xây dựng'],
        'nam_sd' => ['năm sử dụng'],
        'dt_dat' => ['diện tích đất'],
        'tinhtrang' => ['tình trạng'],
        'quytrinh_vh' => ['quy trình vận hành'],
        'quytrinh_bt' => ['quy trình bảo trì'],
        'dv_quanly' => ['đơn vị quản lý'],
        'phuongthuc' => ['phương thức'],
        'chuthich' => ['chú thích'],
        'latitude' => ['vĩ độ'],
        'longitude' => ['kinh độ'],
    ];

    public function __construct(CongdapRepositoryInterface $repository)
    {
        $this->repository = $repository;
        $this->batchSize = 100; // Default batch size
        $this->skipErrors = false; // Default: don't skip errors
    }

    /**
     * Import Excel file data into congdap table
     */
    public function import(UploadedFile $file, array $options = []): array
    {
        $this->batchSize = $options['batch_size'] ?? $this->batchSize;
        $this->skipErrors = $options['skip_errors'] ?? $this->skipErrors;
        
        $startTime = microtime(true);
        $stats = [
            'total_rows' => 0,
            'processed_rows' => 0,
            'successful_imports' => 0,
            'failed_imports' => 0,
            'errors' => [],
            'processing_time' => 0,
            'memory_usage' => 0,
        ];

        try {
            // Parse Excel file
            $data = $this->parseExcelFile($file);
            $stats['total_rows'] = count($data);

            if (empty($data)) {
                throw new Exception('File Excel không chứa dữ liệu hợp lệ.');
            }

            // Process data in batches
            $batches = array_chunk($data, $this->batchSize);
            
            foreach ($batches as $batchIndex => $batch) {
                $batchResult = $this->processBatch($batch, $batchIndex + 1);
                
                $stats['processed_rows'] += $batchResult['processed'];
                $stats['successful_imports'] += $batchResult['successful'];
                $stats['failed_imports'] += $batchResult['failed'];
                $stats['errors'] = array_merge($stats['errors'], $batchResult['errors']);
                
                // Stop processing if we have critical errors and not skipping
                if (!$this->skipErrors && !empty($batchResult['errors'])) {
                    break;
                }
            }

            // Clear cache after successful import
            if ($stats['successful_imports'] > 0) {
                $this->clearCongdapCache();
            }

        } catch (Exception $e) {
            Log::error('Excel import failed: ' . $e->getMessage(), [
                'file' => $file->getClientOriginalName(),
                'exception' => $e
            ]);
            
            $stats['errors'][] = [
                'row' => 0,
                'error' => 'Lỗi xử lý file: ' . $e->getMessage()
            ];
        }

        $stats['processing_time'] = round((microtime(true) - $startTime) * 1000, 2); // milliseconds
        $stats['memory_usage'] = round(memory_get_peak_usage(true) / 1024 / 1024, 2); // MB

        return $stats;
    }

    /**
     * Parse Excel file and extract data
     */
    protected function parseExcelFile(UploadedFile $file): array
    {
        try {
            $spreadsheet = IOFactory::load($file->getPathname());
            $worksheet = $spreadsheet->getActiveSheet();
            $data = $worksheet->toArray();

            if (empty($data)) {
                return [];
            }

            // Get headers from first row
            // Sử dụng mb_strtolower để xử lý đúng tiếng Việt có dấu (UTF-8)
            $headers = array_map(function($header) {
                return trim(mb_strtolower((string)$header, 'UTF-8'));
            }, $data[0]);
            $mappedHeaders = $this->mapHeaders($headers);
            
            // Process data rows (skip header row)
            $processedData = [];
            for ($i = 1; $i < count($data); $i++) {
                $row = $data[$i];
                $mappedRow = [];
                
                foreach ($row as $index => $value) {
                    if (isset($mappedHeaders[$index])) {
                        $mappedRow[$mappedHeaders[$index]] = $this->cleanValue($value);
                    }
                }
                
                // Skip empty rows
                if (!empty(array_filter($mappedRow))) {
                    $mappedRow['_row_number'] = $i + 1; // Store original row number for error reporting
                    $processedData[] = $mappedRow;
                }
            }

            return $processedData;

        } catch (Exception $e) {
            throw new Exception('Không thể đọc file Excel: ' . $e->getMessage());
        }
    }

    /**
     * Map Excel headers to database fields
     */
    protected function mapHeaders(array $headers): array
    {
        $mappedHeaders = [];
        
        foreach ($headers as $index => $header) {
            // Header đã được trim và chuyển thành chữ thường (sử dụng mb_strtolower) từ parseExcelFile
            foreach ($this->columnMapping as $dbField => $possibleHeaders) {
                // So sánh trực tiếp vì các giá trị trong $possibleHeaders đã là chữ thường
                if (in_array($header, $possibleHeaders)) {
                    $mappedHeaders[$index] = $dbField;
                    break;
                }
            }
        }
        
        return $mappedHeaders;
    }

    /**
     * Clean and validate cell value
     */
    protected function cleanValue($value)
    {
        if ($value === null || $value === '') {
            return null;
        }
        
        // Convert to string and trim
        $value = trim((string) $value);
        
        // Return null for empty strings
        return $value === '' ? null : $value;
    }

    /**
     * Process a batch of data
     */
    protected function processBatch(array $batch, int $batchNumber): array
    {
        $result = [
            'processed' => 0,
            'successful' => 0,
            'failed' => 0,
            'errors' => []
        ];

        DB::beginTransaction();
        
        try {
            foreach ($batch as $rowData) {
                $result['processed']++;
                $rowNumber = $rowData['_row_number'] ?? $result['processed'];
                unset($rowData['_row_number']);
                
                try {
                    $validatedData = $this->validateAndTransformRow($rowData, $rowNumber);
                    $this->createCongdapRecord($validatedData);
                    $result['successful']++;
                    
                } catch (Exception $e) {
                    $result['failed']++;
                    $result['errors'][] = [
                        'row' => $rowNumber,
                        'error' => $e->getMessage(),
                        'data' => $rowData
                    ];
                    
                    if (!$this->skipErrors) {
                        throw $e; // Re-throw to stop batch processing
                    }
                }
            }
            
            DB::commit();
            
        } catch (Exception $e) {
            DB::rollBack();
            
            // If not skipping errors, mark all remaining rows as failed
            if (!$this->skipErrors) {
                $result['failed'] = $result['processed'];
                $result['successful'] = 0;
            }
            
            // throw $e; // Không ném lại ngoại lệ, để phương thức import có thể thu thập số liệu và quyết định dừng hay tiếp tục.
        }

        return $result;
    }

    /**
     * Validate and transform row data
     */
    protected function validateAndTransformRow(array $rowData, int $rowNumber): array
    {
        $validatedData = [];

        // Validate required fields (at least name should be present)
        if (empty($rowData['ten'])) {
            throw new Exception("Dòng {$rowNumber}: Tên công trình là bắt buộc.");
        }

        // Map and validate each field
        foreach ($rowData as $field => $value) {
            switch ($field) {
                case 'id_qt':
                    $validatedData['id_qt'] = $this->validateString($value, 20, "Dòng {$rowNumber}: Mã quyết toán", true);
                    break;

                case 'id_xa':
                    $validatedData['id_xa'] = $this->validateString($value, 5, "Dòng {$rowNumber}: Mã xã");
                    break;

                case 'ten':
                    $validatedData['ten'] = $this->validateString($value, 100, "Dòng {$rowNumber}: Tên công trình", true);
                    break;

                case 'quymo_ct':
                    $validatedData['quymo_ct'] = $this->validateString($value, 100, "Dòng {$rowNumber}: Quy mô công trình");
                    break;

                case 'loai_ct':
                    $validatedData['loai_ct'] = $this->validateString($value, 5, "Dòng {$rowNumber}: Loại công trình");
                    break;

                case 'nam_xd':
                    $validatedData['nam_xd'] = $this->validateYear($value, "Dòng {$rowNumber}: Năm xây dựng");
                    break;

                case 'nam_sd':
                    $validatedData['nam_sd'] = $this->validateYear($value, "Dòng {$rowNumber}: Năm sử dụng");
                    break;

                case 'dt_dat':
                    $validatedData['dt_dat'] = $this->validateNumeric($value, "Dòng {$rowNumber}: Diện tích đất");
                    break;

                case 'tinhtrang':
                    $validatedData['tinhtrang'] = $this->validateString($value, 15, "Dòng {$rowNumber}: Tình trạng");
                    break;

                case 'quytrinh_vh':
                    $validatedData['quytrinh_vh'] = $this->validateString($value, 50, "Dòng {$rowNumber}: Quy trình VH");
                    break;

                case 'quytrinh_bt':
                    $validatedData['quytrinh_bt'] = $this->validateString($value, 50, "Dòng {$rowNumber}: Quy trình BT");
                    break;

                case 'dv_quanly':
                    $validatedData['dv_quanly'] = $this->validateString($value, 50, "Dòng {$rowNumber}: Đơn vị quản lý");
                    break;

                case 'phuongthuc':
                    $validatedData['phuongthuc'] = $this->validateString($value, 100, "Dòng {$rowNumber}: Phương thức");
                    break;

                case 'chuthich':
                    $validatedData['chuthich'] = $value; // No length limit for comments
                    break;

                case 'latitude':
                case 'longitude':
                    // Store coordinates for later GeoJSON conversion
                    $validatedData[$field] = $this->validateCoordinate($value, $field, "Dòng {$rowNumber}");
                    break;
            }
        }

        // Convert coordinates to GeoJSON if both latitude and longitude are present
        if (isset($validatedData['latitude']) && isset($validatedData['longitude'])) {
            $validatedData['geom'] = $this->createPointGeometry(
                $validatedData['longitude'],
                $validatedData['latitude']
            );
            unset($validatedData['latitude'], $validatedData['longitude']);
        }

        return $validatedData;
    }

    /**
     * Validate string field
     */
    protected function validateString($value, int $maxLength, string $fieldName, bool $required = false)
    {
        if ($value === null || $value === '') {
            if ($required) {
                throw new Exception("{$fieldName} là bắt buộc.");
            }
            return null;
        }

        $value = trim((string) $value);

        if (strlen($value) > $maxLength) {
            throw new Exception("{$fieldName} không được vượt quá {$maxLength} ký tự.");
        }

        return $value;
    }

    /**
     * Validate year field
     */
    protected function validateYear($value, string $fieldName)
    {
        if ($value === null || $value === '') {
            return null;
        }

        $year = filter_var($value, FILTER_VALIDATE_INT);

        if ($year === false || $year < 1900 || $year > date('Y') + 10) {
            throw new Exception("{$fieldName} phải là năm hợp lệ (1900 - " . (date('Y') + 10) . ").");
        }

        return $year;
    }

    /**
     * Validate numeric field
     */
    protected function validateNumeric($value, string $fieldName)
    {
        if ($value === null || $value === '') {
            return null;
        }

        $numeric = filter_var($value, FILTER_VALIDATE_FLOAT);

        if ($numeric === false || $numeric < 0) {
            throw new Exception("{$fieldName} phải là số dương.");
        }

        return $numeric;
    }

    /**
     * Validate coordinate field
     */
    protected function validateCoordinate($value, string $type, string $context)
    {
        if ($value === null || $value === '') {
            return null;
        }

        $coordinate = filter_var($value, FILTER_VALIDATE_FLOAT);

        if ($coordinate === false) {
            throw new Exception("{$context}: {$type} phải là số thực.");
        }

        // Validate coordinate ranges
        if ($type === 'latitude' && ($coordinate < -90 || $coordinate > 90)) {
            throw new Exception("{$context}: Vĩ độ phải trong khoảng -90 đến 90.");
        }

        if ($type === 'longitude' && ($coordinate < -180 || $coordinate > 180)) {
            throw new Exception("{$context}: Kinh độ phải trong khoảng -180 đến 180.");
        }

        return $coordinate;
    }

    /**
     * Create Point geometry from coordinates
     */
    protected function createPointGeometry(float $longitude, float $latitude)
    {
        $geoJson = [
            'type' => 'Point',
            'coordinates' => [$longitude, $latitude]
        ];

        return Congdap::geometryFromGeoJSON(json_encode($geoJson));
    }

    /**
     * Create congdap record
     */
    protected function createCongdapRecord(array $data): Congdap
    {
        // Use the repository to create the record for consistency
        return $this->repository->create(['properties' => $data]);
    }

    /**
     * Clear congdap cache
     */
    protected function clearCongdapCache(): void
    {
        $cacheTag = 'congdap-list';

        // Check if cache driver supports tags
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];

        if (in_array($driver, $supportedDrivers)) {
            Cache::tags([$cacheTag])->flush();
        } else {
            // Fallback: increment cache version to invalidate all cache
            $versionKey = 'congdap_version';
            $currentVersion = Cache::get($versionKey, 0);
            Cache::put($versionKey, $currentVersion + 1, 7200); // 2 hours
        }
    }
}
