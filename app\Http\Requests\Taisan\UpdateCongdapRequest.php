<?php

namespace App\Http\Requests\Taisan;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCongdapRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type'                => 'sometimes|string|in:Feature',
            'properties'          => 'sometimes|array', // 'sometimes' vì có thể chỉ cập nhật geometry
            'properties.id_qt'    => 'nullable|string|max:20',
            'properties.id_xa'    => 'nullable|string|max:5',
            'properties.ten'      => 'nullable|string|max:100',
            'properties.quymo_ct' => 'nullable|string|max:100',
            'properties.loai_ct'  => 'nullable|string|max:5',
            'properties.nam_xd'   => 'nullable|integer',
            'properties.nam_sd'   => 'nullable|integer',
            'properties.dt_dat'   => 'nullable|numeric',
            'properties.tinhtrang'    => 'nullable|string|max:15',
            'properties.quytrinh_vh'  => 'nullable|string|max:50',
            'properties.quytrinh_bt'  => 'nullable|string|max:50',
            'properties.dv_quanly'    => 'nullable|string|max:50',
            'properties.phuongthuc'   => 'nullable|string|max:100',
            'properties.chuthich'     => 'nullable|string',
            'geometry'            => ['nullable', 'array', function($attribute, $value, $fail) {
                if ($value && (!isset($value['type']) || !isset($value['coordinates']))) {
                    $fail('The geometry must be a valid GeoJSON object with type and coordinates.');
                }
            }]
        ];
    }

    public function messages(): array
    {
        return [
            'properties.array'    => 'Trường properties phải là một object.',
            'geometry.array'      => 'Geometry phải là một GeoJSON object hợp lệ.',
            // Thêm messages cho các trường con của properties nếu cần
        ];
    }
}