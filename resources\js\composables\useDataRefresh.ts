import { ref } from 'vue'

// Global state để quản lý việc refresh data
const refreshTrigger = ref(0)
const refreshCallbacks = new Set<() => void>()

export function useDataRefresh() {
  // Hàm để trigger refresh cho tất cả components đang lắng nghe
  const triggerRefresh = () => {
    refreshTrigger.value++
    refreshCallbacks.forEach(callback => {
      try {
        callback()
      } catch (error) {
        console.error('Error in refresh callback:', error)
      }
    })
  }

  // Hàm để đăng ký callback refresh
  const onRefresh = (callback: () => void) => {
    refreshCallbacks.add(callback)
    
    // Trả về hàm để unregister callback
    return () => {
      refreshCallbacks.delete(callback)
    }
  }

  return {
    refreshTrigger,
    triggerRefresh,
    onRefresh
  }
}
