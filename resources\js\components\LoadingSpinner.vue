<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'white';
  overlay?: boolean;
  text?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  color: 'primary',
  overlay: false,
  text: ''
});

// Compute size in pixels
const sizeInPx = computed(() => {
  switch (props.size) {
    case 'sm': return '16px';
    case 'md': return '24px';
    case 'lg': return '36px';
    case 'xl': return '48px';
    default: return '24px';
  }
});

// Compute border color
const borderColor = computed(() => {
  switch (props.color) {
    case 'primary': return '#0ea5e9'; // sky-500
    case 'secondary': return '#6b7280'; // gray-500
    case 'white': return '#ffffff';
    default: return '#0ea5e9';
  }
});
</script>

<template>
  <div :class="{ 'loading-overlay': overlay }" class="loading-container">
    <div 
      class="spinner" 
      :style="{ 
        width: sizeInPx, 
        height: sizeInPx, 
        borderColor: `${borderColor} transparent transparent transparent` 
      }"
    ></div>
    <div v-if="text" class="loading-text">{{ text }}</div>
  </div>
</template>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  border: 3px solid;
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.loading-text {
  margin-top: 8px;
  font-size: 16px;
  color: #4b5563; /* gray-600 */
  background-color: white;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
