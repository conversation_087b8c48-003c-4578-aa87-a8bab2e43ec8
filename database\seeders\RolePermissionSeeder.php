<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('🗑️ Cleaning old data...');
        
        // Tắt foreign key constraints tạm thời (PostgreSQL)
        DB::statement('SET session_replication_role = replica;');
        
        // Xóa tất cả relationships trước
        DB::table('model_has_permissions')->truncate();
        DB::table('model_has_roles')->truncate(); 
        DB::table('role_has_permissions')->truncate();
        
        // Xóa personal access tokens
        DB::table('personal_access_tokens')->truncate();
        
        // Xóa users, roles và permissions
        User::truncate();
        Role::truncate();
        Permission::truncate();
        
        // Bật lại foreign key constraints
        DB::statement('SET session_replication_role = DEFAULT;');
        
        $this->command->info('✅ Old data cleaned successfully.');
        $this->command->info('🚀 Creating new permissions, roles and users...');

        // 1. Tạo permissions (mở rộng thêm)
        $permissions = [
            // Data permissions
            ['name' => 'view_data', 'vi_name' => 'Xem dữ liệu', 'group' => 'data'],
            ['name' => 'create_data', 'vi_name' => 'Tạo dữ liệu', 'group' => 'data'],
            ['name' => 'edit_data', 'vi_name' => 'Sửa dữ liệu', 'group' => 'data'],
            ['name' => 'delete_data', 'vi_name' => 'Xoá dữ liệu', 'group' => 'data'],
            ['name' => 'import_data', 'vi_name' => 'Import dữ liệu', 'group' => 'data'],
            ['name' => 'export_data', 'vi_name' => 'Export dữ liệu', 'group' => 'data'],
            
            // Report permissions
            ['name' => 'create_reports', 'vi_name' => 'Tạo báo cáo', 'group' => 'report'],
            ['name' => 'export_reports', 'vi_name' => 'Xuất báo cáo', 'group' => 'report'],
            
            // User management permissions
            ['name' => 'view_users', 'vi_name' => 'Xem người dùng', 'group' => 'user'],
            ['name' => 'create_users', 'vi_name' => 'Tạo người dùng', 'group' => 'user'],
            ['name' => 'edit_users', 'vi_name' => 'Sửa người dùng', 'group' => 'user'],
            ['name' => 'delete_users', 'vi_name' => 'Xoá người dùng', 'group' => 'user'],
            ['name' => 'assign_roles', 'vi_name' => 'Phân quyền', 'group' => 'user'],
            
            // System permissions
            ['name' => 'view_logs', 'vi_name' => 'Xem logs', 'group' => 'system'],
            ['name' => 'backup_restore', 'vi_name' => 'Sao lưu/Khôi phục', 'group' => 'system'],
            
            // Calculation permissions
            ['name' => 'calculate_wear', 'vi_name' => 'Tính toán hao mòn', 'group' => 'calculation'],
            ['name' => 'advanced_calculation', 'vi_name' => 'Tính toán nâng cao', 'group' => 'calculation'],
            
            // Profile permissions
            ['name' => 'update_profile', 'vi_name' => 'Cập nhật hồ sơ', 'group' => 'profile'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']], 
                [
                    'vi_name' => $permission['vi_name'],
                    'group' => $permission['group'] ?? 'general'
                ]
            );
        }

        // 2. Tạo Base Roles (template roles)
        $baseRoles = [
            [
                'name' => 'super_admin',
                'vi_name' => 'Siêu quản trị',
                'description' => 'Toàn quyền hệ thống',
                'permissions' => Permission::all()->pluck('name')->toArray()
            ],
            [
                'name' => 'admin',
                'vi_name' => 'Quản trị viên',
                'description' => 'Quản lý người dùng và dữ liệu',
                'permissions' => [
                    'view_data', 'create_data', 'edit_data', 'delete_data', 'import_data', 'export_data', 'create_reports', 'export_reports',
                    'view_users', 'create_users', 'edit_users', 'assign_roles',
                    'calculate_wear', 'update_profile'
                ]
            ],
            [
                'name' => 'data_specialist',
                'vi_name' => 'Chuyên viên Dữ liệu',
                'description' => 'Chuyên về quản lý dữ liệu',
                'permissions' => [
                    'view_data', 'create_data', 'edit_data', 'import_data', 'export_data',
                    'calculate_wear', 'update_profile'
                ]
            ],
            [
                'name' => 'report_specialist',
                'vi_name' => 'Chuyên viên Báo cáo',
                'description' => 'Chuyên về tạo và quản lý báo cáo',
                'permissions' => [
                    'view_data', 'create_reports', 'export_reports',
                    'calculate_wear', 'update_profile'
                ]
            ],
            [
                'name' => 'user_manager',
                'vi_name' => 'Quản lý Người dùng',
                'description' => 'Quản lý tài khoản người dùng',
                'permissions' => [
                    'view_data', 'view_users', 'create_users', 'edit_users', 'assign_roles',
                    'update_profile'
                ]
            ],
            [
                'name' => 'viewer',
                'vi_name' => 'Người xem',
                'description' => 'Chỉ xem dữ liệu và báo cáo',
                'permissions' => ['view_data', 'update_profile']
            ]
        ];

        foreach ($baseRoles as $roleData) {
            $role = Role::firstOrCreate(
                ['name' => $roleData['name']], 
                [
                    'vi_name' => $roleData['vi_name'],
                    'description' => $roleData['description'] ?? null
                ]
            );
            $role->syncPermissions($roleData['permissions']);
        }

        // 3. Tạo users với role linh hoạt
        $this->command->info('Creating users with flexible roles...');

        // Admin User
        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Nguyễn Văn Tấn',
                'password' => Hash::make('123'),
                'id_xa' => '27595',
            ]
        );
        $adminUser->assignRole('admin');
        $adminToken = $adminUser->createToken('admin-seed-token')->plainTextToken;
        Log::info("Admin Token (<EMAIL>): " . $adminToken);
        $this->command->line("Admin Token (<EMAIL>): <fg=yellow>$adminToken</>");

        // Specialist với nhiều chức năng (combo role + extra permissions)
        $specialistUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Đặng Thị Hà',
                'password' => Hash::make('123'),
                'id_xa' => null,
            ]
        );
        // Gán role chính
        $specialistUser->assignRole('data_specialist');
        // Thêm permissions từ role khác (linh hoạt)
        $specialistUser->givePermissionTo([
            'create_reports', 'export_reports', // từ report_specialist
            'view_users', 'create_users' // từ user_manager
        ]);
        
        $specialistToken = $specialistUser->createToken('specialist-seed-token')->plainTextToken;
        Log::info("Specialist Token (<EMAIL>): " . $specialistToken);
        $this->command->line("Specialist Token (<EMAIL>): <fg=yellow>$specialistToken</>");

        // Guest User
        $guestUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Ngô Cường',
                'password' => Hash::make('123'),
                'id_xa' => null,
            ]
        );
        $guestUser->assignRole('viewer');
        
        $guestToken = $guestUser->createToken('guest-seed-token')->plainTextToken;
        Log::info("Guest Token (<EMAIL>): " . $guestToken);
        $this->command->line("Guest Token (<EMAIL>): <fg=yellow>$guestToken</>");

        $this->command->info('Users and flexible permissions created successfully.');
    }

    /**
     * Helper method để tạo custom role cho user cụ thể
     */
    private function createCustomRole(string $name, string $viName, array $permissions): Role
    {
        $role = Role::create([
            'name' => $name,
            'vi_name' => $viName,
            'is_custom' => true // flag để đánh dấu role tùy chỉnh
        ]);
        
        $role->givePermissionTo($permissions);
        return $role;
    }
}