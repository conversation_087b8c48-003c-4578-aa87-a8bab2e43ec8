<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\PageController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Route::post('login/guest', [AuthenticatedSessionController::class, 'loginAsGuest'])
//     ->name('login.guest');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/', function () {
        return Inertia::render('Map');
    })->name('map');

    Route::get('dashboard', function () {
        return Inertia::render('Dashboard');
    })->middleware(['auth', 'verified'])->name('dashboard');
});

/* Route::get('/', function () {
    return redirect('/map');
}); */

/* Route::get('/map', [PageController::class, 'map'])->name('map'); */
Route::get('/data-management', [PageController::class, 'dataManagement'])->name('data-management');
Route::get('/reports', [PageController::class, 'reports'])->name('reports');
Route::get('/admin', [PageController::class, 'admin'])->name('admin');

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
